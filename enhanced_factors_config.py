# coding=utf-8
"""
增强因子配置文件
管理所有增强因子的参数和阈值
"""

# ==================== 买入决策阈值配置 ====================

# 综合评分阈值
ENHANCED_BUY_THRESHOLDS = {
    'enhanced_overall_score': 60,      # 综合评分最低要求
    'buy_confidence_score': 65,        # 买入信心度最低要求
    'market_environment_score': 40,    # 市场环境最低要求
    'fundamental_score': 45,           # 基本面最低要求
    'money_flow_score': 40,            # 资金流向最低要求
    'risk_score': 35,                  # 风险评分最低要求（分数越高风险越低）
}

# 单项因子阈值
INDIVIDUAL_FACTOR_THRESHOLDS = {
    # 市场环境因子
    'market_index_change': {'min': -0.03, 'max': 0.10},     # 大盘涨跌幅范围
    'market_volume_ratio': {'min': 0.8, 'max': 3.0},        # 大盘成交量比范围
    'market_sentiment_score': {'min': 30, 'max': 100},      # 市场情绪评分
    'northbound_flow': {'min': -100, 'max': 1000},          # 北向资金流入（亿元）
    
    # 基本面因子
    'pe_ratio_current': {'min': 5, 'max': 50},              # PE比率范围
    'pb_ratio': {'min': 0.5, 'max': 8},                     # PB比率范围
    'roe_ttm': {'min': 0.05, 'max': 1.0},                   # ROE范围
    'debt_to_equity': {'min': 0, 'max': 0.8},               # 资产负债率上限
    'eps_growth_yoy': {'min': -0.5, 'max': 2.0},            # EPS增长率范围
    'revenue_growth_yoy': {'min': -0.3, 'max': 1.0},        # 营收增长率范围
    'current_ratio': {'min': 1.0, 'max': 5.0},              # 流动比率范围
    
    # 技术面因子
    'realized_volatility': {'min': 0.005, 'max': 0.08},     # 已实现波动率范围
    'trend_intensity': {'min': -0.1, 'max': 0.1},           # 趋势强度范围
    'momentum_strength': {'min': -0.2, 'max': 0.3},         # 动量强度范围
    'vwap_distance': {'min': -0.05, 'max': 0.05},           # VWAP距离范围
    
    # 资金流向因子
    'main_force_inflow': {'min': -5000, 'max': 20000},      # 主力资金流入范围（万元）
    'large_order_ratio': {'min': 0.1, 'max': 0.8},         # 大单比例范围
    'institutional_ownership': {'min': 0.05, 'max': 0.9},   # 机构持股比例范围
    'retail_sentiment': {'min': 20, 'max': 90},             # 散户情绪范围
    
    # 风险因子
    'beta_stability': {'min': 0.3, 'max': 2.0},             # Beta稳定性范围
    'downside_deviation': {'min': 0.005, 'max': 0.1},       # 下行偏差范围
    'value_at_risk': {'min': 0.01, 'max': 0.15},            # VaR范围
    'liquidity_risk': {'min': 0.1, 'max': 0.9},             # 流动性风险范围
}

# ==================== 评分权重配置 ====================

# 综合评分权重
COMPOSITE_SCORE_WEIGHTS = {
    'market_environment_score': 0.25,    # 市场环境权重
    'fundamental_score': 0.25,           # 基本面权重
    'enhanced_technical_score': 0.20,    # 技术面权重
    'money_flow_score': 0.20,            # 资金流向权重
    'risk_score': 0.10,                  # 风险权重
}

# 市场环境评分权重
MARKET_ENVIRONMENT_WEIGHTS = {
    'market_index_change': 0.3,          # 大盘涨跌幅权重
    'market_volume_ratio': 0.2,          # 成交量比权重
    'market_sentiment_score': 0.3,       # 市场情绪权重
    'advance_decline_ratio': 0.1,        # 涨跌比权重
    'northbound_flow': 0.1,              # 北向资金权重
}

# 基本面评分权重
FUNDAMENTAL_WEIGHTS = {
    'pe_ratio_current': 0.2,             # PE比率权重
    'roe_ttm': 0.25,                     # ROE权重
    'eps_growth_yoy': 0.2,               # EPS增长权重
    'debt_to_equity': 0.15,              # 负债率权重
    'revenue_growth_yoy': 0.1,           # 营收增长权重
    'current_ratio': 0.1,                # 流动比率权重
}

# 技术面评分权重
TECHNICAL_WEIGHTS = {
    'trend_intensity': 0.3,              # 趋势强度权重
    'momentum_strength': 0.25,           # 动量强度权重
    'realized_volatility': 0.2,          # 波动率权重
    'vwap_distance': 0.15,               # VWAP距离权重
    'price_volume_divergence': 0.1,      # 量价背离权重
}

# 资金流向评分权重
MONEY_FLOW_WEIGHTS = {
    'main_force_inflow': 0.4,            # 主力资金权重
    'institutional_ownership': 0.25,     # 机构持股权重
    'large_order_ratio': 0.2,            # 大单比例权重
    'retail_sentiment': 0.15,            # 散户情绪权重
}

# 风险评分权重
RISK_WEIGHTS = {
    'beta_stability': 0.25,              # Beta稳定性权重
    'downside_deviation': 0.3,           # 下行偏差权重
    'value_at_risk': 0.25,               # VaR权重
    'liquidity_risk': 0.2,               # 流动性风险权重
}

# ==================== 仓位管理配置 ====================

# 基础仓位配置
POSITION_CONFIG = {
    'base_position_ratio': 0.1,          # 基础仓位比例（10%）
    'max_position_ratio': 0.15,          # 最大单股仓位比例（15%）
    'min_position_ratio': 0.03,          # 最小单股仓位比例（3%）
    'max_total_position': 0.95,          # 最大总仓位比例（95%）
}

# 仓位调整因子
POSITION_ADJUSTMENT_FACTORS = {
    'confidence_multiplier': {
        'min': 0.5,                      # 信心度最小倍数
        'max': 1.5,                      # 信心度最大倍数
        'base': 50,                      # 信心度基准值
    },
    'risk_multiplier': {
        'min': 0.3,                      # 风险调整最小倍数
        'max': 1.2,                      # 风险调整最大倍数
        'base': 50,                      # 风险评分基准值
    },
    'market_multiplier': {
        'min': 0.4,                      # 市场环境最小倍数
        'max': 1.3,                      # 市场环境最大倍数
        'base': 50,                      # 市场评分基准值
    }
}

# ==================== 时间因子配置 ====================

# 交易时间限制
TRADING_TIME_CONFIG = {
    'allowed_hours': [9.5, 10, 10.5, 11, 11.5, 13, 13.5, 14, 14.5],  # 允许交易的小时
    'avoid_hours': [9.25, 15],                                        # 避免交易的时间
    'preferred_hours': [10, 10.5, 13.5, 14],                         # 优选交易时间
}

# 季节性效应配置
SEASONAL_CONFIG = {
    'strong_months': [1, 2, 3, 11, 12],      # 强势月份
    'weak_months': [6, 7, 8],                # 弱势月份
    'neutral_months': [4, 5, 9, 10],         # 中性月份
    'earnings_months': [1, 4, 7, 10],        # 财报月份
}

# ==================== 事件驱动配置 ====================

# 事件权重配置
EVENT_WEIGHTS = {
    'earnings_announcement': 5,           # 财报发布加分
    'dividend_announcement': 3,           # 分红公告加分
    'analyst_upgrade': 4,                 # 分析师上调加分
    'policy_announcement': 6,             # 政策公告加分
    'index_inclusion': 8,                 # 指数纳入加分
}

# 事件时间窗口（天）
EVENT_TIME_WINDOWS = {
    'earnings_announcement': 7,           # 财报前7天
    'dividend_announcement': 30,          # 分红前30天
    'analyst_upgrade': 3,                 # 评级调整后3天
    'policy_announcement': 5,             # 政策发布后5天
    'index_inclusion': 10,                # 指数纳入前10天
}

# ==================== 风险控制配置 ====================

# 止损止盈配置
RISK_CONTROL_CONFIG = {
    'stop_loss_pct': -0.05,              # 基础止损比例（-5%）
    'take_profit_pct': 0.08,             # 基础止盈比例（8%）
    'trailing_stop_pct': 0.03,           # 移动止损比例（3%）
    'max_drawdown_pct': -0.15,           # 最大回撤限制（-15%）
}

# 动态风控调整
DYNAMIC_RISK_CONFIG = {
    'high_volatility_threshold': 0.04,   # 高波动率阈值
    'high_volatility_stop_loss': -0.03,  # 高波动时止损比例
    'low_confidence_stop_loss': -0.04,   # 低信心度止损比例
    'market_stress_stop_loss': -0.03,    # 市场压力时止损比例
}

# ==================== 数据质量配置 ====================

# 数据有效性检查
DATA_QUALITY_CONFIG = {
    'min_history_days': 20,              # 最少历史数据天数
    'max_missing_ratio': 0.2,            # 最大缺失数据比例
    'outlier_threshold': 3,              # 异常值检测阈值（标准差倍数）
    'min_volume_threshold': 100000,      # 最小成交量阈值
}

# 因子有效性检查
FACTOR_VALIDITY_CONFIG = {
    'pe_ratio_range': [1, 200],          # PE比率有效范围
    'pb_ratio_range': [0.1, 20],         # PB比率有效范围
    'roe_range': [-1, 2],                # ROE有效范围
    'volatility_range': [0.001, 0.2],    # 波动率有效范围
}

# ==================== 配置使用函数 ====================

def get_buy_threshold(factor_name):
    """获取买入阈值"""
    return ENHANCED_BUY_THRESHOLDS.get(factor_name, 50)

def get_factor_range(factor_name):
    """获取因子有效范围"""
    return INDIVIDUAL_FACTOR_THRESHOLDS.get(factor_name, {'min': 0, 'max': 100})

def get_score_weight(category, factor_name):
    """获取评分权重"""
    weight_dict = {
        'market': MARKET_ENVIRONMENT_WEIGHTS,
        'fundamental': FUNDAMENTAL_WEIGHTS,
        'technical': TECHNICAL_WEIGHTS,
        'money_flow': MONEY_FLOW_WEIGHTS,
        'risk': RISK_WEIGHTS,
    }
    return weight_dict.get(category, {}).get(factor_name, 0.1)

def get_position_config():
    """获取仓位配置"""
    return POSITION_CONFIG.copy()

def get_risk_config():
    """获取风险控制配置"""
    return RISK_CONTROL_CONFIG.copy()

def is_trading_time_allowed(hour):
    """检查是否允许交易时间"""
    return hour in TRADING_TIME_CONFIG['allowed_hours']

def get_seasonal_effect(month):
    """获取季节性效应"""
    if month in SEASONAL_CONFIG['strong_months']:
        return 0.05
    elif month in SEASONAL_CONFIG['weak_months']:
        return -0.05
    else:
        return 0.0

def get_event_weight(event_type):
    """获取事件权重"""
    return EVENT_WEIGHTS.get(event_type, 0)

# ==================== 配置验证函数 ====================

def validate_config():
    """验证配置的一致性"""
    errors = []
    
    # 检查权重是否合理
    total_weight = sum(COMPOSITE_SCORE_WEIGHTS.values())
    if abs(total_weight - 1.0) > 0.01:
        errors.append(f"综合评分权重总和不等于1: {total_weight}")
    
    # 检查阈值是否合理
    for factor, threshold in ENHANCED_BUY_THRESHOLDS.items():
        if not 0 <= threshold <= 100:
            errors.append(f"阈值超出范围 {factor}: {threshold}")
    
    # 检查仓位配置
    pos_config = POSITION_CONFIG
    if pos_config['min_position_ratio'] >= pos_config['max_position_ratio']:
        errors.append("最小仓位比例大于等于最大仓位比例")
    
    return errors

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
    
    # 显示关键配置
    print("\n📊 关键配置摘要:")
    print(f"  综合评分阈值: {ENHANCED_BUY_THRESHOLDS['enhanced_overall_score']}")
    print(f"  信心度阈值: {ENHANCED_BUY_THRESHOLDS['buy_confidence_score']}")
    print(f"  基础仓位比例: {POSITION_CONFIG['base_position_ratio']:.1%}")
    print(f"  止损比例: {RISK_CONTROL_CONFIG['stop_loss_pct']:.1%}")
    print(f"  止盈比例: {RISK_CONTROL_CONFIG['take_profit_pct']:.1%}")
