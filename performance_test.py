#!/usr/bin/env python
# coding=utf-8
"""
策略性能优化测试脚本
测试优化前后的性能差异，验证优化效果
"""

import sys
import os
import time
import datetime
import pandas as pd
import numpy as np
from unittest.mock import Mock, MagicMock
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class MockContext:
    """模拟策略上下文"""
    def __init__(self):
        self.now = datetime.datetime.now()
        self.log = Mock()
        self.positions = {}
        self.positions_cost = {}
        self.max_positions = 10
        self.today_bought_symbols = set()
        self.last_buy_date = self.now.date()
        
        # 模拟账户
        self.account_mock = Mock()
        self.account_mock.positions.return_value = []
        self.account_mock.cash.available = 100000
        
        # 初始化性能优化组件
        self.global_data_cache = None
        self.timestamp_cache = None
        self.trix_cache_manager = None
        self.batch_db_manager = None
        
    def account(self):
        return self.account_mock

def mock_history_n(symbol, count, frequency='1d', fields=None, end_time=None):
    """模拟历史数据获取函数"""
    # 模拟网络延迟
    time.sleep(0.01)  # 10ms延迟模拟网络请求
    
    if isinstance(symbol, list):
        # 批量获取
        result = {}
        for sym in symbol:
            result[sym] = generate_mock_data(count)
        return result
    else:
        # 单个获取
        return generate_mock_data(count)

def generate_mock_data(count):
    """生成模拟数据"""
    dates = pd.date_range(end=datetime.datetime.now(), periods=count, freq='D')
    base_price = 10.0
    
    data = []
    for i, date in enumerate(dates):
        price = base_price + np.random.normal(0, 0.5)
        data.append({
            'date': date,
            'open': price,
            'high': price * 1.02,
            'low': price * 0.98,
            'close': price,
            'volume': 1000000 + np.random.randint(-100000, 100000)
        })
    
    return pd.DataFrame(data)

def test_data_cache_performance():
    """测试数据缓存性能"""
    print("🧪 测试数据缓存性能...")
    
    # 导入优化组件
    from main import GlobalDataCacheManager
    
    context = MockContext()
    cache_manager = GlobalDataCacheManager(context)
    
    # 测试股票列表
    test_symbols = [f"SHSE.60000{i:02d}" for i in range(50)]
    
    # 模拟原始方式（无缓存）
    print("📊 测试原始方式（无缓存）...")
    start_time = time.time()
    
    for symbol in test_symbols:
        data = mock_history_n(symbol, 60, '1d', ['close'])
    
    original_time = time.time() - start_time
    print(f"   原始方式耗时: {original_time:.2f}秒")
    
    # 模拟优化方式（使用缓存）
    print("📊 测试优化方式（使用缓存）...")
    start_time = time.time()
    
    # 第一次批量获取（会缓存）
    cached_data = cache_manager.batch_preload_data(test_symbols, 60, '1d', ['close'])
    
    # 第二次获取（使用缓存）
    for symbol in test_symbols:
        data = cache_manager.get_cached_data(symbol, 60, '1d', ['close'])
    
    optimized_time = time.time() - start_time
    print(f"   优化方式耗时: {optimized_time:.2f}秒")
    
    # 计算性能提升
    improvement = (original_time - optimized_time) / original_time * 100
    print(f"   性能提升: {improvement:.1f}%")
    
    # 输出缓存统计
    stats = cache_manager.get_stats()
    print(f"   缓存命中率: {stats['cache_hit_rate']:.1f}%")
    print(f"   缓存项数量: {stats['total_cached_items']}")
    
    return improvement

def test_timestamp_cache_performance():
    """测试时间戳缓存性能"""
    print("\n🧪 测试时间戳缓存性能...")
    
    from main import TimestampCacheManager, get_cached_timestamp
    
    context = MockContext()
    timestamp_cache = TimestampCacheManager(context)
    context.timestamp_cache = timestamp_cache
    
    # 测试原始方式
    print("📊 测试原始方式（重复格式化）...")
    start_time = time.time()
    
    for i in range(1000):
        timestamp = context.now.strftime('%Y-%m-%d %H:%M:%S')
    
    original_time = time.time() - start_time
    print(f"   原始方式耗时: {original_time:.4f}秒")
    
    # 测试优化方式
    print("📊 测试优化方式（使用缓存）...")
    start_time = time.time()
    
    for i in range(1000):
        timestamp = get_cached_timestamp(context)
    
    optimized_time = time.time() - start_time
    print(f"   优化方式耗时: {optimized_time:.4f}秒")
    
    # 计算性能提升
    improvement = (original_time - optimized_time) / original_time * 100
    print(f"   性能提升: {improvement:.1f}%")
    
    return improvement

def test_trix_cache_performance():
    """测试TRIX缓存性能"""
    print("\n🧪 测试TRIX缓存性能...")
    
    from main import TrixCacheManager
    
    context = MockContext()
    trix_cache = TrixCacheManager(context)
    
    # 生成测试数据
    test_data = np.random.random(60) * 10 + 10
    test_symbols = [f"SHSE.60000{i:02d}" for i in range(20)]
    
    # 模拟TRIX计算函数
    def mock_calculate_trix(data):
        time.sleep(0.005)  # 5ms计算时间
        return np.random.random(len(data))
    
    # 测试原始方式（重复计算）
    print("📊 测试原始方式（重复计算）...")
    start_time = time.time()
    
    for symbol in test_symbols:
        for _ in range(3):  # 模拟同一股票被多次计算
            trix = mock_calculate_trix(test_data)
    
    original_time = time.time() - start_time
    print(f"   原始方式耗时: {original_time:.3f}秒")
    
    # 测试优化方式（使用缓存）
    print("📊 测试优化方式（使用缓存）...")
    start_time = time.time()
    
    for symbol in test_symbols:
        # 第一次计算并缓存
        cached_trix = trix_cache.get_cached_trix(symbol)
        if cached_trix is None:
            trix = mock_calculate_trix(test_data)
            trix_cache.cache_trix(symbol, trix)
        
        # 后续使用缓存
        for _ in range(2):
            cached_trix = trix_cache.get_cached_trix(symbol)
    
    optimized_time = time.time() - start_time
    print(f"   优化方式耗时: {optimized_time:.3f}秒")
    
    # 计算性能提升
    improvement = (original_time - optimized_time) / original_time * 100
    print(f"   性能提升: {improvement:.1f}%")
    
    return improvement

def test_batch_database_performance():
    """测试批量数据库操作性能"""
    print("\n🧪 测试批量数据库操作性能...")
    
    from main import BatchDatabaseManager
    
    context = MockContext()
    batch_manager = BatchDatabaseManager(context)
    
    # 生成测试记录
    test_records = []
    for i in range(100):
        record = {
            'Timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'Symbol': f'SHSE.60000{i:02d}',
            'Action': 'BUY',
            'Price': 10.0 + i * 0.1,
            'Volume': 1000
        }
        test_records.append(record)
    
    # 模拟单个写入
    def mock_save_single(record):
        time.sleep(0.001)  # 1ms写入时间
    
    # 测试原始方式（单个写入）
    print("📊 测试原始方式（单个写入）...")
    start_time = time.time()
    
    for record in test_records:
        mock_save_single(record)
    
    original_time = time.time() - start_time
    print(f"   原始方式耗时: {original_time:.3f}秒")
    
    # 测试优化方式（批量写入）
    print("📊 测试优化方式（批量写入）...")
    start_time = time.time()
    
    # 模拟批量添加
    for record in test_records:
        batch_manager.add_record(record)
    
    # 强制刷新
    batch_manager.flush_records()
    
    optimized_time = time.time() - start_time
    print(f"   优化方式耗时: {optimized_time:.3f}秒")
    
    # 计算性能提升
    improvement = (original_time - optimized_time) / original_time * 100
    print(f"   性能提升: {improvement:.1f}%")
    
    return improvement

def run_comprehensive_performance_test():
    """运行综合性能测试"""
    print("🚀 策略性能优化综合测试")
    print("=" * 60)
    
    improvements = []
    
    try:
        # 测试数据缓存
        improvement = test_data_cache_performance()
        improvements.append(('数据缓存', improvement))
    except Exception as e:
        print(f"❌ 数据缓存测试失败: {e}")
    
    try:
        # 测试时间戳缓存
        improvement = test_timestamp_cache_performance()
        improvements.append(('时间戳缓存', improvement))
    except Exception as e:
        print(f"❌ 时间戳缓存测试失败: {e}")
    
    try:
        # 测试TRIX缓存
        improvement = test_trix_cache_performance()
        improvements.append(('TRIX缓存', improvement))
    except Exception as e:
        print(f"❌ TRIX缓存测试失败: {e}")
    
    try:
        # 测试批量数据库操作
        improvement = test_batch_database_performance()
        improvements.append(('批量数据库', improvement))
    except Exception as e:
        print(f"❌ 批量数据库测试失败: {e}")
    
    # 输出总结
    print("\n📊 性能优化测试总结")
    print("=" * 60)
    
    total_improvement = 0
    for name, improvement in improvements:
        print(f"✅ {name:12}: {improvement:6.1f}% 性能提升")
        total_improvement += improvement
    
    if improvements:
        avg_improvement = total_improvement / len(improvements)
        print(f"\n🎯 平均性能提升: {avg_improvement:.1f}%")
        
        if avg_improvement > 50:
            print("🏆 优化效果显著！")
        elif avg_improvement > 20:
            print("✅ 优化效果良好！")
        else:
            print("⚠️ 优化效果一般")
    
    print("\n💡 优化建议:")
    print("1. 在生产环境中启用所有缓存机制")
    print("2. 根据实际情况调整缓存过期时间")
    print("3. 监控内存使用情况，及时清理过期缓存")
    print("4. 在回测模式下可以使用更大的批量大小")

if __name__ == "__main__":
    # 模拟必要的函数
    import builtins
    builtins.history_n = mock_history_n
    
    run_comprehensive_performance_test()
