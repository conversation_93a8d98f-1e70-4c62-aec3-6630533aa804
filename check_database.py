# coding=utf-8
"""
数据库检查脚本
检查买卖记录匹配性和数据完整性
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime

def check_database_integrity():
    """检查数据库完整性"""
    print('🔍 数据库完整性检查')
    print('=' * 50)
    
    # 检查数据库文件
    db_files = ['data/trades.db', 'data/positions.db']
    
    for db_file in db_files:
        print(f'\n📁 检查数据库: {db_file}')
        
        if not os.path.exists(db_file):
            print(f'❌ 数据库文件不存在: {db_file}')
            continue
            
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [t[0] for t in cursor.fetchall()]
            print(f'📊 包含表: {tables}')
            
            # 检查trades表
            if 'trades' in tables:
                print('\n📈 trades表分析:')
                
                # 记录总数
                cursor.execute('SELECT COUNT(*) FROM trades')
                total_count = cursor.fetchone()[0]
                print(f'  总记录数: {total_count:,}')
                
                if total_count > 0:
                    # 交易类型分布
                    cursor.execute('SELECT action, COUNT(*) FROM trades GROUP BY action')
                    actions = dict(cursor.fetchall())
                    print(f'  交易类型分布: {actions}')
                    
                    # 检查买卖匹配
                    buy_count = actions.get('BUY', 0)
                    sell_count = actions.get('SELL', 0)
                    print(f'  买入记录: {buy_count:,}')
                    print(f'  卖出记录: {sell_count:,}')
                    print(f'  匹配率: {min(buy_count, sell_count) / max(buy_count, sell_count) * 100:.1f}%' if max(buy_count, sell_count) > 0 else '无法计算')
                    
                    # 检查字段完整性
                    cursor.execute('PRAGMA table_info(trades)')
                    columns = [col[1] for col in cursor.fetchall()]
                    print(f'  字段总数: {len(columns)}')
                    
                    # 关键字段检查
                    key_fields = ['price', 'volume', 'symbol', 'timestamp', 'action']
                    missing_fields = [field for field in key_fields if field not in columns]
                    if missing_fields:
                        print(f'  ❌ 缺失关键字段: {missing_fields}')
                    else:
                        print(f'  ✅ 关键字段完整')
                    
                    # 技术指标字段检查
                    tech_fields = [col for col in columns if any(tech in col.lower() for tech in ['ma', 'rsi', 'trix', 'macd', 'bb', 'atr'])]
                    print(f'  📊 技术指标字段: {len(tech_fields)}个')
                    if len(tech_fields) > 0:
                        print(f'    示例: {tech_fields[:5]}')
                    
                    # 增强指标字段检查
                    enhanced_fields = [col for col in columns if any(enh in col.lower() for enh in ['enhanced', 'factor', 'score', 'momentum'])]
                    print(f'  🚀 增强指标字段: {len(enhanced_fields)}个')
                    if len(enhanced_fields) > 0:
                        print(f'    示例: {enhanced_fields[:5]}')
                    
                    # 数据质量检查
                    print('\n🔍 数据质量检查:')
                    
                    # 检查空值
                    for field in ['price', 'volume', 'symbol']:
                        if field in columns:
                            cursor.execute(f'SELECT COUNT(*) FROM trades WHERE {field} IS NULL OR {field} = ""')
                            null_count = cursor.fetchone()[0]
                            print(f'  {field}空值: {null_count} ({null_count/total_count*100:.1f}%)')
                    
                    # 检查异常值
                    cursor.execute('SELECT MIN(price), MAX(price), AVG(price) FROM trades WHERE price > 0')
                    price_stats = cursor.fetchone()
                    if price_stats[0]:
                        print(f'  价格范围: {price_stats[0]:.2f} - {price_stats[1]:.2f}, 平均: {price_stats[2]:.2f}')
                    
                    # 时间范围
                    cursor.execute('SELECT MIN(timestamp), MAX(timestamp) FROM trades')
                    time_range = cursor.fetchone()
                    print(f'  时间范围: {time_range[0]} 到 {time_range[1]}')
                    
                    # 检查盈亏数据
                    cursor.execute('SELECT COUNT(*) FROM trades WHERE net_profit_pct_sell IS NOT NULL')
                    profit_records = cursor.fetchone()[0]
                    if profit_records > 0:
                        print(f'  有盈亏数据的记录: {profit_records:,}')
                        
                        cursor.execute('SELECT AVG(net_profit_pct_sell), COUNT(CASE WHEN net_profit_pct_sell > 0 THEN 1 END) FROM trades WHERE net_profit_pct_sell IS NOT NULL')
                        profit_stats = cursor.fetchone()
                        win_rate = profit_stats[1] / profit_records * 100 if profit_records > 0 else 0
                        print(f'  平均收益率: {profit_stats[0]:.2f}%')
                        print(f'  胜率: {win_rate:.1f}%')
            
            # 检查positions表
            if 'positions' in tables:
                print('\n💼 positions表分析:')
                cursor.execute('SELECT COUNT(*) FROM positions')
                pos_count = cursor.fetchone()[0]
                print(f'  持仓记录数: {pos_count:,}')
                
                if pos_count > 0:
                    cursor.execute('SELECT COUNT(*) FROM positions WHERE is_open = 1')
                    open_pos = cursor.fetchone()[0]
                    print(f'  开仓记录: {open_pos:,}')
                    print(f'  平仓记录: {pos_count - open_pos:,}')
            
            conn.close()
            
        except Exception as e:
            print(f'❌ 检查数据库 {db_file} 时出错: {e}')

def check_factor_analysis_capability():
    """检查因子分析能力"""
    print('\n\n🧠 因子分析能力检查')
    print('=' * 50)
    
    # 检查分析脚本
    analysis_scripts = [
        '胜率分析器.py',
        '因子有效性分析器.py',
        'scripts/data_manager.py'
    ]
    
    for script in analysis_scripts:
        if os.path.exists(script):
            print(f'✅ 分析脚本存在: {script}')
        else:
            print(f'❌ 分析脚本缺失: {script}')
    
    # 检查是否有足够的数据进行分析
    if os.path.exists('data/trades.db'):
        try:
            conn = sqlite3.connect('data/trades.db')
            cursor = conn.cursor()
            
            # 检查买入记录数量
            cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
            buy_count = cursor.fetchone()[0]
            
            print(f'\n📊 分析数据评估:')
            print(f'  买入记录数: {buy_count:,}')
            
            if buy_count >= 100:
                print('  ✅ 数据量充足，可进行高质量分析')
            elif buy_count >= 30:
                print('  ⚠️ 数据量一般，可进行基础分析')
            else:
                print('  ❌ 数据量不足，分析结果可能不可靠')
            
            # 检查因子字段完整性
            cursor.execute('PRAGMA table_info(trades)')
            columns = [col[1] for col in cursor.fetchall()]
            
            factor_categories = {
                '技术指标': ['ma', 'rsi', 'macd', 'bb', 'kdj', 'trix'],
                '价格动量': ['price_change', 'momentum', 'gap'],
                '成交量': ['volume', 'obv', 'relative_volume'],
                '波动率': ['atr', 'volatility', 'bb_width'],
                '基本面': ['pe_ratio', 'pb_ratio', 'roe']
            }
            
            print(f'\n📋 因子类别覆盖:')
            for category, keywords in factor_categories.items():
                matching_fields = [col for col in columns if any(kw in col.lower() for kw in keywords)]
                coverage = len(matching_fields)
                print(f'  {category}: {coverage}个字段')
                if coverage > 0:
                    print(f'    示例: {matching_fields[:3]}')
            
            conn.close()
            
        except Exception as e:
            print(f'❌ 检查分析能力时出错: {e}')

if __name__ == '__main__':
    check_database_integrity()
    check_factor_analysis_capability()
