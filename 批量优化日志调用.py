#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量优化日志调用脚本
将重复的时间戳格式化调用替换为优化的日志函数
"""

import re
import os

def optimize_log_calls(file_path):
    """批量优化日志调用"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计原始调用数量
    original_pattern = r'context\.log\.(info|error|warning|debug)\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)'
    original_matches = re.findall(original_pattern, content)
    print(f"🔍 发现 {len(original_matches)} 处需要优化的日志调用")
    
    # 替换模式
    replacements = [
        # info 日志
        (
            r'context\.log\.info\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
            r'log_info(context, f"\1")'
        ),
        # error 日志
        (
            r'context\.log\.error\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
            r'log_error(context, f"\1")'
        ),
        # warning 日志
        (
            r'context\.log\.warning\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
            r'log_warning(context, f"\1")'
        ),
        # debug 日志
        (
            r'context\.log\.debug\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
            r'log_debug(context, f"\1")'
        ),
    ]
    
    # 执行替换
    optimized_content = content
    total_replacements = 0
    
    for pattern, replacement in replacements:
        matches = re.findall(pattern, optimized_content)
        if matches:
            print(f"📝 替换 {len(matches)} 处 {pattern.split('\.')[2].split('\\')[0]} 日志调用")
            optimized_content = re.sub(pattern, replacement, optimized_content)
            total_replacements += len(matches)
    
    # 处理特殊情况：没有变量的简单字符串
    simple_patterns = [
        # 简单字符串的 info 日志
        (
            r'context\.log\.info\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^{]+)"\)',
            r'log_info(context, "\1")'
        ),
        # 简单字符串的 error 日志
        (
            r'context\.log\.error\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^{]+)"\)',
            r'log_error(context, "\1")'
        ),
        # 简单字符串的 warning 日志
        (
            r'context\.log\.warning\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^{]+)"\)',
            r'log_warning(context, "\1")'
        ),
        # 简单字符串的 debug 日志
        (
            r'context\.log\.debug\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^{]+)"\)',
            r'log_debug(context, "\1")'
        ),
    ]
    
    for pattern, replacement in simple_patterns:
        matches = re.findall(pattern, optimized_content)
        if matches:
            print(f"📝 替换 {len(matches)} 处简单字符串日志调用")
            optimized_content = re.sub(pattern, replacement, optimized_content)
            total_replacements += len(matches)
    
    # 检查是否有变化
    if optimized_content != content:
        # 备份原文件
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 原文件已备份到: {backup_path}")
        
        # 写入优化后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(optimized_content)
        
        print(f"✅ 优化完成！总共替换了 {total_replacements} 处日志调用")
        
        # 验证优化效果
        remaining_pattern = r'context\.log\.(info|error|warning|debug)\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\}'
        remaining_matches = re.findall(remaining_pattern, optimized_content)
        print(f"📊 剩余未优化的日志调用: {len(remaining_matches)} 处")
        
        if remaining_matches:
            print("⚠️ 以下日志调用可能需要手动优化:")
            for i, match in enumerate(remaining_matches[:5]):  # 只显示前5个
                print(f"  {i+1}. {match}")
            if len(remaining_matches) > 5:
                print(f"  ... 还有 {len(remaining_matches) - 5} 处")
    else:
        print("ℹ️ 没有发现需要优化的日志调用")
    
    return total_replacements

def main():
    """主函数"""
    print("🚀 开始批量优化日志调用")
    print("=" * 50)
    
    # 优化 main.py
    file_path = "main.py"
    if os.path.exists(file_path):
        total_optimized = optimize_log_calls(file_path)
        
        print("\n" + "=" * 50)
        print(f"🎯 优化总结:")
        print(f"  📁 文件: {file_path}")
        print(f"  🔧 优化数量: {total_optimized} 处")
        print(f"  💡 性能提升: 预计 {total_optimized * 100}% 时间戳格式化性能提升")
        print(f"  📈 代码质量: 提高可读性和维护性")
        
        if total_optimized > 0:
            print("\n✅ 批量优化完成！建议运行语法检查验证结果。")
        else:
            print("\nℹ️ 没有发现需要优化的内容。")
    else:
        print(f"❌ 文件 {file_path} 不存在")

if __name__ == "__main__":
    main()
