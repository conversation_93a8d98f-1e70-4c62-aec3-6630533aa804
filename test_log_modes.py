# coding=utf-8
"""
日志模式测试脚本
快速测试3种日志模式的效果
"""

import os
import time

def test_log_mode(mode):
    """测试指定的日志模式"""
    print(f'\n🔧 测试日志模式 {mode}')
    print('=' * 40)
    
    # 切换模式
    os.system(f'python set_mode.py {mode}')
    
    print(f'\n📊 模式 {mode} 特点:')
    mode_descriptions = {
        1: {
            'name': '🚀 极简模式',
            'features': [
                '✅ 只显示关键进度信息',
                '❌ 不显示个股分析详情',
                '❌ 不显示数据格式检查',
                '❌ 不显示TRIX计算详情',
                '🎯 最高性能，适合回测'
            ]
        },
        2: {
            'name': '📊 详细模式',
            'features': [
                '✅ 显示进度和部分调试信息',
                '✅ 显示前10只股票的详细信息',
                '✅ 显示数据格式和TRIX计算',
                '⚖️ 平衡性能和信息量',
                '🔧 适合策略开发调试'
            ]
        },
        3: {
            'name': '🔍 调试模式',
            'features': [
                '✅ 显示所有调试信息',
                '✅ 显示前20只股票的完整信息',
                '✅ 显示详细的计算过程',
                '✅ 显示异常和错误详情',
                '🐛 完整调试，适合问题排查'
            ]
        }
    }
    
    if mode in mode_descriptions:
        desc = mode_descriptions[mode]
        print(f'  {desc["name"]}')
        for feature in desc['features']:
            print(f'    {feature}')
    
    print(f'\n💡 现在可以运行策略测试模式 {mode} 的效果')
    print('   策略运行时将按照此模式输出日志')

def show_comparison():
    """显示三种模式的对比"""
    print('\n📈 三种日志模式对比')
    print('=' * 60)
    
    comparison_table = [
        ('特性', '模式1-极简', '模式2-详细', '模式3-调试'),
        ('性能', '🚀🚀🚀', '🚀🚀', '🚀'),
        ('日志量', '⭐', '⭐⭐⭐', '⭐⭐⭐⭐⭐'),
        ('进度信息', '✅', '✅', '✅'),
        ('个股详情', '❌', '✅(前10只)', '✅(前20只)'),
        ('数据检查', '❌', '✅', '✅'),
        ('TRIX计算', '❌', '✅', '✅'),
        ('异常详情', '❌', '✅', '✅'),
        ('适用场景', '生产回测', '开发调试', '问题排查'),
    ]
    
    # 打印表格
    for row in comparison_table:
        print(f'{row[0]:<8} {row[1]:<12} {row[2]:<15} {row[3]:<15}')

def show_performance_impact():
    """显示性能影响"""
    print('\n⚡ 性能影响预估')
    print('=' * 40)
    
    performance_data = [
        ('模式', '日志输出量', '性能影响', '预筛选耗时'),
        ('1-极简', '最少', '几乎无影响', '0.1-0.2秒'),
        ('2-详细', '适中', '轻微影响', '0.2-0.4秒'),
        ('3-调试', '最多', '明显影响', '0.4-0.8秒'),
    ]
    
    for row in performance_data:
        print(f'{row[0]:<8} {row[1]:<10} {row[2]:<12} {row[3]:<12}')
    
    print('\n💡 建议:')
    print('  📊 日常回测: 使用模式1 (极简)')
    print('  🔧 策略开发: 使用模式2 (详细)')
    print('  🐛 问题排查: 使用模式3 (调试)')

def main():
    """主函数"""
    print('🧪 日志模式测试工具')
    print('=' * 50)
    
    # 显示当前模式
    os.system('python switch_log_mode.py')
    
    # 显示对比
    show_comparison()
    show_performance_impact()
    
    print('\n🔧 快速切换命令:')
    print('  python set_mode.py 1   # 极简模式')
    print('  python set_mode.py 2   # 详细模式')
    print('  python set_mode.py 3   # 调试模式')
    
    print('\n🧪 测试建议:')
    print('  1. 先用模式3运行策略，观察完整日志')
    print('  2. 再用模式1运行策略，对比性能差异')
    print('  3. 根据需要选择合适的模式')
    
    # 交互式测试
    while True:
        print('\n' + '='*50)
        choice = input('选择操作 (1-3=切换模式, q=退出): ').strip().lower()
        
        if choice == 'q':
            print('👋 测试完成，再见！')
            break
        elif choice in ['1', '2', '3']:
            test_log_mode(int(choice))
        else:
            print('❌ 无效选择，请输入 1、2、3 或 q')

if __name__ == '__main__':
    main()
