#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强买入信号系统
基于多因子融合的智能买入信号生成
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, timedelta


class EnhancedBuySignalSystem:
    """增强买入信号系统"""
    
    def __init__(self, context):
        self.context = context
        self.signal_weights = {
            'technical': 0.40,      # 技术面权重
            'volume': 0.25,         # 成交量权重  
            'momentum': 0.20,       # 动量权重
            'trend': 0.15          # 趋势权重
        }
        
        # 信号阈值配置 (已调整为更宽松的设置)
        self.thresholds = {
            'min_total_score': 50,          # 最低总分 (从70降低到50)
            'min_technical_score': 40,      # 最低技术分 (从60降低到40)
            'min_volume_score': 30,         # 最低成交量分 (从50降低到30)
            'min_momentum_score': 35,       # 最低动量分 (从55降低到35)
            'min_trend_score': 30          # 最低趋势分 (从50降低到30)
        }
        
    def generate_enhanced_buy_signal(self, symbol, data):
        """
        生成增强买入信号
        
        参数:
        - symbol: 股票代码
        - data: 股票历史数据
        
        返回:
        - signal_result: 信号结果字典
        """
        try:
            if data is None or len(data) < 30:
                return self._create_signal_result(False, 0, "数据不足")
            
            # 1. 技术面分析 (40%权重)
            technical_score = self._calculate_technical_score(data)
            
            # 2. 成交量分析 (25%权重)
            volume_score = self._calculate_volume_score(data)
            
            # 3. 动量分析 (20%权重)
            momentum_score = self._calculate_momentum_score(data)
            
            # 4. 趋势分析 (15%权重)
            trend_score = self._calculate_trend_score(data)
            
            # 计算加权总分
            total_score = (
                technical_score * self.signal_weights['technical'] +
                volume_score * self.signal_weights['volume'] +
                momentum_score * self.signal_weights['momentum'] +
                trend_score * self.signal_weights['trend']
            )
            
            # 判断是否满足买入条件
            buy_signal = self._evaluate_buy_conditions(
                total_score, technical_score, volume_score, 
                momentum_score, trend_score
            )
            
            # 创建详细结果
            signal_result = self._create_signal_result(
                buy_signal, total_score, "多因子分析",
                {
                    'technical_score': technical_score,
                    'volume_score': volume_score,
                    'momentum_score': momentum_score,
                    'trend_score': trend_score,
                    'weights': self.signal_weights
                }
            )

            # 🔍 添加详细调试日志
            self._debug_signal_analysis(symbol, signal_result)

            return signal_result
            
        except Exception as e:
            self.context.log.error(f"生成增强买入信号异常 {symbol}: {str(e)}")
            return self._create_signal_result(False, 0, f"计算异常: {str(e)}")
    
    def _calculate_technical_score(self, data):
        """计算技术面评分"""
        try:
            # 兼容不同数据格式
            if hasattr(data, 'values'):  # pandas DataFrame
                close_prices = data['close'].values
                high_prices = data['high'].values
                low_prices = data['low'].values
            elif isinstance(data, dict):  # 字典格式
                close_prices = np.array(data['close'])
                high_prices = np.array(data['high'])
                low_prices = np.array(data['low'])
            else:  # 列表格式
                close_prices = np.array([item['close'] for item in data])
                high_prices = np.array([item['high'] for item in data])
                low_prices = np.array([item['low'] for item in data])
            
            score = 0
            
            # 1. TRIX金叉确认 (30分)
            trix_score = self._check_trix_golden_cross(close_prices)
            score += trix_score * 0.3
            
            # 2. 均线多头排列 (25分)
            ma_score = self._check_ma_bullish_alignment(close_prices)
            score += ma_score * 0.25
            
            # 3. RSI超卖反弹 (20分)
            rsi_score = self._check_rsi_oversold_bounce(close_prices)
            score += rsi_score * 0.2
            
            # 4. 布林带位置 (15分)
            bb_score = self._check_bollinger_position(close_prices)
            score += bb_score * 0.15
            
            # 5. 支撑阻力 (10分)
            support_score = self._check_support_resistance(high_prices, low_prices, close_prices)
            score += support_score * 0.1
            
            return min(100, max(0, score))
            
        except Exception as e:
            self.context.log.warning(f"技术面评分计算异常: {str(e)}")
            return 0
    
    def _calculate_volume_score(self, data):
        """计算成交量评分"""
        try:
            # 兼容不同数据格式
            if hasattr(data, 'values'):  # pandas DataFrame
                volume = data['volume'].values
                close_prices = data['close'].values
            elif isinstance(data, dict):  # 字典格式
                volume = np.array(data['volume'])
                close_prices = np.array(data['close'])
            else:  # 列表格式
                volume = np.array([item['volume'] for item in data])
                close_prices = np.array([item['close'] for item in data])
            
            score = 0
            
            # 1. 成交量突破 (40分)
            volume_breakout_score = self._check_volume_breakout(volume)
            score += volume_breakout_score * 0.4
            
            # 2. 量价配合 (35分)
            price_volume_score = self._check_price_volume_coordination(close_prices, volume)
            score += price_volume_score * 0.35
            
            # 3. 相对成交量 (25分)
            relative_volume_score = self._check_relative_volume(volume)
            score += relative_volume_score * 0.25
            
            return min(100, max(0, score))
            
        except Exception as e:
            self.context.log.warning(f"成交量评分计算异常: {str(e)}")
            return 0
    
    def _calculate_momentum_score(self, data):
        """计算动量评分"""
        try:
            # 兼容不同数据格式
            if hasattr(data, 'values'):  # pandas DataFrame
                close_prices = data['close'].values
                high_prices = data['high'].values
                low_prices = data['low'].values
            elif isinstance(data, dict):  # 字典格式
                close_prices = np.array(data['close'])
                high_prices = np.array(data['high'])
                low_prices = np.array(data['low'])
            else:  # 列表格式
                close_prices = np.array([item['close'] for item in data])
                high_prices = np.array([item['high'] for item in data])
                low_prices = np.array([item['low'] for item in data])
            
            score = 0
            
            # 1. MACD金叉 (35分)
            macd_score = self._check_macd_golden_cross(close_prices)
            score += macd_score * 0.35
            
            # 2. KDJ金叉 (30分)
            kdj_score = self._check_kdj_golden_cross(high_prices, low_prices, close_prices)
            score += kdj_score * 0.3
            
            # 3. 威廉指标 (20分)
            williams_score = self._check_williams_r(high_prices, low_prices, close_prices)
            score += williams_score * 0.2
            
            # 4. 动量指标 (15分)
            momentum_indicator_score = self._check_momentum_indicator(close_prices)
            score += momentum_indicator_score * 0.15
            
            return min(100, max(0, score))
            
        except Exception as e:
            self.context.log.warning(f"动量评分计算异常: {str(e)}")
            return 0
    
    def _calculate_trend_score(self, data):
        """计算趋势评分"""
        try:
            # 兼容不同数据格式
            if hasattr(data, 'values'):  # pandas DataFrame
                close_prices = data['close'].values
            elif isinstance(data, dict):  # 字典格式
                close_prices = np.array(data['close'])
            else:  # 列表格式
                close_prices = np.array([item['close'] for item in data])
            
            score = 0
            
            # 1. 趋势强度 (40分)
            trend_strength_score = self._check_trend_strength(close_prices)
            score += trend_strength_score * 0.4
            
            # 2. 趋势方向 (35分)
            trend_direction_score = self._check_trend_direction(close_prices)
            score += trend_direction_score * 0.35
            
            # 3. 趋势持续性 (25分)
            trend_persistence_score = self._check_trend_persistence(close_prices)
            score += trend_persistence_score * 0.25
            
            return min(100, max(0, score))
            
        except Exception as e:
            self.context.log.warning(f"趋势评分计算异常: {str(e)}")
            return 0
    
    def _check_trix_golden_cross(self, close_prices):
        """检查TRIX金叉"""
        try:
            if len(close_prices) < 50:
                return 0
                
            # 计算TRIX
            ema1 = talib.EMA(close_prices, timeperiod=14)
            ema2 = talib.EMA(ema1, timeperiod=14)
            ema3 = talib.EMA(ema2, timeperiod=14)
            
            trix = np.zeros_like(close_prices)
            for i in range(1, len(ema3)):
                if ema3[i-1] != 0:
                    trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100
            
            # 计算TRIX信号线
            trix_signal = talib.EMA(trix, timeperiod=9)
            
            # 检查金叉
            if (trix[-1] > trix_signal[-1] and 
                trix[-2] <= trix_signal[-2] and
                trix[-1] > trix[-2]):  # TRIX上升
                return 100
            elif trix[-1] > trix_signal[-1]:  # 在信号线上方
                return 70
            elif trix[-1] > trix[-2]:  # TRIX上升
                return 50
            else:
                return 20
                
        except Exception:
            return 0
    
    def _check_ma_bullish_alignment(self, close_prices):
        """检查均线多头排列"""
        try:
            if len(close_prices) < 60:
                return 0
                
            ma5 = talib.SMA(close_prices, timeperiod=5)
            ma10 = talib.SMA(close_prices, timeperiod=10)
            ma20 = talib.SMA(close_prices, timeperiod=20)
            ma60 = talib.SMA(close_prices, timeperiod=60)
            
            # 检查多头排列: 价格 > MA5 > MA10 > MA20 > MA60
            if (close_prices[-1] > ma5[-1] > ma10[-1] > ma20[-1] > ma60[-1]):
                return 100
            elif (close_prices[-1] > ma5[-1] > ma10[-1] > ma20[-1]):
                return 80
            elif (close_prices[-1] > ma5[-1] > ma10[-1]):
                return 60
            elif close_prices[-1] > ma5[-1]:
                return 40
            else:
                return 10
                
        except Exception:
            return 0
    
    def _check_volume_breakout(self, volume):
        """检查成交量突破"""
        try:
            if len(volume) < 20:
                return 0
                
            # 计算20日平均成交量
            avg_volume_20 = np.mean(volume[-20:])
            current_volume = volume[-1]
            
            # 成交量倍数
            volume_ratio = current_volume / avg_volume_20 if avg_volume_20 > 0 else 0
            
            if volume_ratio >= 2.0:  # 2倍以上
                return 100
            elif volume_ratio >= 1.5:  # 1.5倍以上
                return 80
            elif volume_ratio >= 1.2:  # 1.2倍以上
                return 60
            elif volume_ratio >= 1.0:  # 平均水平
                return 40
            else:
                return 20
                
        except Exception:
            return 0
    
    def _evaluate_buy_conditions(self, total_score, technical_score, volume_score, momentum_score, trend_score):
        """评估买入条件"""
        # 总分要求
        if total_score < self.thresholds['min_total_score']:
            return False
            
        # 各项分数要求
        if technical_score < self.thresholds['min_technical_score']:
            return False
            
        if volume_score < self.thresholds['min_volume_score']:
            return False
            
        if momentum_score < self.thresholds['min_momentum_score']:
            return False
            
        if trend_score < self.thresholds['min_trend_score']:
            return False
            
        return True
    
    def _create_signal_result(self, buy_signal, score, reason, details=None):
        """创建信号结果"""
        return {
            'buy_signal': buy_signal,
            'score': score,
            'reason': reason,
            'details': details or {},
            'timestamp': datetime.now(),
            'signal_type': 'enhanced_multi_factor'
        }

    def _debug_signal_analysis(self, symbol, signal_result):
        """调试信号分析结果"""
        try:
            details = signal_result.get('details', {})

            # 每10个股票输出一次详细分析
            if hasattr(self, '_debug_counter'):
                self._debug_counter += 1
            else:
                self._debug_counter = 1

            # 如果有买入信号或者是调试模式，输出详细信息
            if signal_result['buy_signal'] or self._debug_counter % 10 == 0:
                status = "✅ 买入信号" if signal_result['buy_signal'] else "❌ 无信号"
                self.context.log.info(f"🔍 {symbol} {status}:")
                self.context.log.info(f"  📊 总分: {signal_result['score']:.1f} (要求≥{self.thresholds['min_total_score']})")

                if 'technical_score' in details:
                    tech_status = "✅" if details['technical_score'] >= self.thresholds['min_technical_score'] else "❌"
                    self.context.log.info(f"  {tech_status} 技术面: {details['technical_score']:.1f} (要求≥{self.thresholds['min_technical_score']})")

                if 'volume_score' in details:
                    vol_status = "✅" if details['volume_score'] >= self.thresholds['min_volume_score'] else "❌"
                    self.context.log.info(f"  {vol_status} 成交量: {details['volume_score']:.1f} (要求≥{self.thresholds['min_volume_score']})")

                if 'momentum_score' in details:
                    mom_status = "✅" if details['momentum_score'] >= self.thresholds['min_momentum_score'] else "❌"
                    self.context.log.info(f"  {mom_status} 动量: {details['momentum_score']:.1f} (要求≥{self.thresholds['min_momentum_score']})")

                if 'trend_score' in details:
                    trend_status = "✅" if details['trend_score'] >= self.thresholds['min_trend_score'] else "❌"
                    self.context.log.info(f"  {trend_status} 趋势: {details['trend_score']:.1f} (要求≥{self.thresholds['min_trend_score']})")

                # 如果没有买入信号，分析具体失败原因
                if not signal_result['buy_signal']:
                    failures = []
                    if signal_result['score'] < self.thresholds['min_total_score']:
                        failures.append(f"总分不足({signal_result['score']:.1f}<{self.thresholds['min_total_score']})")
                    if details.get('technical_score', 0) < self.thresholds['min_technical_score']:
                        failures.append(f"技术面不足({details.get('technical_score', 0):.1f}<{self.thresholds['min_technical_score']})")
                    if details.get('volume_score', 0) < self.thresholds['min_volume_score']:
                        failures.append(f"成交量不足({details.get('volume_score', 0):.1f}<{self.thresholds['min_volume_score']})")
                    if details.get('momentum_score', 0) < self.thresholds['min_momentum_score']:
                        failures.append(f"动量不足({details.get('momentum_score', 0):.1f}<{self.thresholds['min_momentum_score']})")
                    if details.get('trend_score', 0) < self.thresholds['min_trend_score']:
                        failures.append(f"趋势不足({details.get('trend_score', 0):.1f}<{self.thresholds['min_trend_score']})")

                    if failures:
                        self.context.log.info(f"  🔍 失败原因: {'; '.join(failures)}")

        except Exception as e:
            self.context.log.warning(f"调试信号分析异常: {str(e)}")
    
    # 其他辅助方法的简化实现
    def _check_rsi_oversold_bounce(self, close_prices):
        """RSI超卖反弹检查"""
        try:
            rsi = talib.RSI(close_prices, timeperiod=14)
            if rsi[-1] > 30 and rsi[-2] <= 30:  # 从超卖区反弹
                return 100
            elif 30 < rsi[-1] < 50:  # 在合理区间
                return 60
            else:
                return 20
        except:
            return 0
    
    def _check_bollinger_position(self, close_prices):
        """布林带位置检查"""
        try:
            upper, middle, lower = talib.BBANDS(close_prices, timeperiod=20)
            current_price = close_prices[-1]
            
            if current_price <= lower[-1]:  # 在下轨附近
                return 80
            elif current_price <= middle[-1]:  # 在中轨下方
                return 60
            else:
                return 30
        except:
            return 0
    
    def _check_support_resistance(self, high_prices, low_prices, close_prices):
        """支撑阻力检查"""
        try:
            # 简化实现：检查是否在近期低点附近
            recent_low = np.min(low_prices[-10:])
            current_price = close_prices[-1]
            
            if abs(current_price - recent_low) / recent_low < 0.05:  # 5%以内
                return 80
            else:
                return 40
        except:
            return 0
    
    def _check_price_volume_coordination(self, close_prices, volume):
        """量价配合检查"""
        try:
            price_change = (close_prices[-1] - close_prices[-2]) / close_prices[-2]
            volume_change = (volume[-1] - volume[-2]) / volume[-2] if volume[-2] > 0 else 0
            
            if price_change > 0 and volume_change > 0:  # 价涨量增
                return 100
            elif price_change > 0:  # 仅价涨
                return 60
            else:
                return 30
        except:
            return 0
    
    def _check_relative_volume(self, volume):
        """相对成交量检查"""
        try:
            if len(volume) < 5:
                return 0
            avg_volume = np.mean(volume[-5:])
            current_volume = volume[-1]
            return min(100, (current_volume / avg_volume) * 50) if avg_volume > 0 else 0
        except:
            return 0
    
    def _check_macd_golden_cross(self, close_prices):
        """MACD金叉检查"""
        try:
            macd, signal, hist = talib.MACD(close_prices)
            if macd[-1] > signal[-1] and macd[-2] <= signal[-2]:
                return 100
            elif macd[-1] > signal[-1]:
                return 70
            else:
                return 30
        except:
            return 0
    
    def _check_kdj_golden_cross(self, high_prices, low_prices, close_prices):
        """KDJ金叉检查"""
        try:
            k, d = talib.STOCH(high_prices, low_prices, close_prices)
            if k[-1] > d[-1] and k[-2] <= d[-2] and k[-1] < 80:
                return 100
            elif k[-1] > d[-1]:
                return 60
            else:
                return 30
        except:
            return 0
    
    def _check_williams_r(self, high_prices, low_prices, close_prices):
        """威廉指标检查"""
        try:
            williams = talib.WILLR(high_prices, low_prices, close_prices)
            if williams[-1] > -20:  # 超买
                return 20
            elif williams[-1] > -50:  # 中性
                return 60
            elif williams[-1] > -80:  # 接近超卖
                return 80
            else:  # 超卖
                return 100
        except:
            return 0
    
    def _check_momentum_indicator(self, close_prices):
        """动量指标检查"""
        try:
            momentum = talib.MOM(close_prices, timeperiod=10)
            if momentum[-1] > 0 and momentum[-1] > momentum[-2]:
                return 100
            elif momentum[-1] > 0:
                return 60
            else:
                return 20
        except:
            return 0
    
    def _check_trend_strength(self, close_prices):
        """趋势强度检查"""
        try:
            # 使用ADX指标
            high_prices = close_prices  # 简化处理
            low_prices = close_prices
            adx = talib.ADX(high_prices, low_prices, close_prices)
            
            if adx[-1] > 25:  # 强趋势
                return 100
            elif adx[-1] > 20:  # 中等趋势
                return 70
            else:  # 弱趋势
                return 40
        except:
            return 50
    
    def _check_trend_direction(self, close_prices):
        """趋势方向检查"""
        try:
            ma20 = talib.SMA(close_prices, timeperiod=20)
            if close_prices[-1] > ma20[-1] and ma20[-1] > ma20[-5]:
                return 100
            elif close_prices[-1] > ma20[-1]:
                return 70
            else:
                return 30
        except:
            return 50
    
    def _check_trend_persistence(self, close_prices):
        """趋势持续性检查"""
        try:
            # 检查连续上涨天数
            up_days = 0
            for i in range(len(close_prices)-1, 0, -1):
                if close_prices[i] > close_prices[i-1]:
                    up_days += 1
                else:
                    break
            
            return min(100, up_days * 20)  # 每连续上涨一天得20分
        except:
            return 0
