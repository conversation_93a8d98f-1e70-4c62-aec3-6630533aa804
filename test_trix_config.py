# coding=utf-8
"""
TRIX配置测试脚本
测试统一的TRIX计算函数和配置控制
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_trix_unified_calculation():
    """测试统一的TRIX计算函数"""
    print('🧪 TRIX统一计算函数测试')
    print('=' * 50)
    
    try:
        # 导入配置和计算函数
        from config import get_config_value
        from main import calculate_trix_unified
        
        # 生成测试数据
        np.random.seed(42)
        base_price = 100
        price_changes = np.random.normal(0, 0.02, 60)  # 60天的价格变化
        close_prices = [base_price]
        
        for change in price_changes:
            new_price = close_prices[-1] * (1 + change)
            close_prices.append(max(new_price, 1))  # 确保价格为正
        
        close_prices = np.array(close_prices, dtype=np.float64)
        
        print(f'📊 测试数据: {len(close_prices)}天收盘价')
        print(f'   价格范围: {close_prices.min():.2f} - {close_prices.max():.2f}')
        
        # 获取当前配置
        trix_period = get_config_value('TRIX_EMA_PERIOD', 3)
        use_talib = get_config_value('USE_TALIB_TRIX', False)
        
        print(f'\n🔧 当前配置:')
        print(f'   TRIX周期: {trix_period}')
        print(f'   使用talib: {use_talib}')
        
        # 测试不同配置
        test_configs = [
            {'period': 3, 'use_talib': False, 'name': '周期3-自定义计算'},
            {'period': 3, 'use_talib': True, 'name': '周期3-talib计算'},
            {'period': 7, 'use_talib': False, 'name': '周期7-自定义计算'},
            {'period': 14, 'use_talib': False, 'name': '周期14-自定义计算'},
        ]
        
        print(f'\n📈 TRIX计算结果对比:')
        print('-' * 60)
        
        for config in test_configs:
            try:
                trix = calculate_trix_unified(
                    close_prices, 
                    config['period'], 
                    config['use_talib']
                )
                
                if trix is not None and len(trix) > 0:
                    # 过滤掉NaN值
                    valid_trix = trix[~np.isnan(trix)]
                    if len(valid_trix) > 0:
                        last_values = valid_trix[-3:] if len(valid_trix) >= 3 else valid_trix
                        print(f'{config["name"]:<20} | 有效值: {len(valid_trix):3d} | 最后3值: {last_values}')
                    else:
                        print(f'{config["name"]:<20} | 无有效值')
                else:
                    print(f'{config["name"]:<20} | 计算失败')
                    
            except Exception as e:
                print(f'{config["name"]:<20} | 异常: {str(e)}')
        
        # 测试默认配置（从config获取）
        print(f'\n🎯 使用默认配置计算:')
        try:
            default_trix = calculate_trix_unified(close_prices)
            if default_trix is not None and len(default_trix) > 0:
                valid_trix = default_trix[~np.isnan(default_trix)]
                if len(valid_trix) > 0:
                    print(f'   成功计算TRIX，有效值: {len(valid_trix)}个')
                    print(f'   最后5个值: {valid_trix[-5:]}')
                    
                    # 测试反转信号
                    if len(valid_trix) >= 2:
                        current = valid_trix[-1]
                        prev = valid_trix[-2]
                        reversal_signal = current > prev
                        print(f'   反转信号: {reversal_signal} (当前: {current:.6f}, 前值: {prev:.6f})')
                else:
                    print('   计算结果无有效值')
            else:
                print('   计算失败')
        except Exception as e:
            print(f'   异常: {str(e)}')
        
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_config_consistency():
    """测试配置一致性"""
    print(f'\n🔧 配置一致性测试')
    print('=' * 50)
    
    try:
        from config import get_config_value
        
        # 检查TRIX相关配置
        configs_to_check = [
            ('TRIX_EMA_PERIOD', 3, 'TRIX EMA周期'),
            ('USE_TALIB_TRIX', False, '是否使用talib'),
            ('TRIX_REVERSAL_PERIOD', 3, 'TRIX拐点周期（已废弃）'),
            ('TRIX_SELL_EMA_PERIOD', 7, 'TRIX卖出周期'),
        ]
        
        print('📋 当前TRIX相关配置:')
        for config_name, default_value, description in configs_to_check:
            try:
                value = get_config_value(config_name, default_value)
                print(f'   {config_name:<25} = {value:<10} ({description})')
            except Exception as e:
                print(f'   {config_name:<25} = 获取失败: {e}')
        
        # 检查配置建议
        trix_period = get_config_value('TRIX_EMA_PERIOD', 3)
        trix_reversal_period = get_config_value('TRIX_REVERSAL_PERIOD', 3)
        
        print(f'\n💡 配置建议:')
        if trix_period == trix_reversal_period:
            print('   ✅ TRIX周期配置一致，使用统一周期')
        else:
            print(f'   ⚠️ TRIX周期不一致: EMA周期={trix_period}, 拐点周期={trix_reversal_period}')
            print('   建议: 使用TRIX_EMA_PERIOD统一控制所有TRIX计算')
        
        use_talib = get_config_value('USE_TALIB_TRIX', False)
        if use_talib:
            print('   📊 使用talib直接计算TRIX（更快但可能精度略低）')
        else:
            print('   🔧 使用自定义三重EMA计算TRIX（更精确但稍慢）')
        
        return True
        
    except Exception as e:
        print(f'❌ 配置检查失败: {e}')
        return False

def show_usage_examples():
    """显示使用示例"""
    print(f'\n📖 使用示例')
    print('=' * 50)
    
    examples = [
        {
            'title': '修改TRIX周期为7',
            'config': "TRIX_EMA_PERIOD = 7",
            'effect': '所有TRIX计算（买入信号、预筛选、拐点信号）都使用7周期'
        },
        {
            'title': '启用talib计算',
            'config': "USE_TALIB_TRIX = True",
            'effect': '使用talib.TRIX()直接计算，速度更快'
        },
        {
            'title': '使用自定义计算',
            'config': "USE_TALIB_TRIX = False",
            'effect': '使用三重EMA自定义计算，精度更高'
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f'{i}. {example["title"]}')
        print(f'   配置: {example["config"]}')
        print(f'   效果: {example["effect"]}')
        print()

def main():
    """主函数"""
    print('🧪 TRIX统一配置测试工具')
    print('=' * 60)
    
    # 运行测试
    test1_result = test_trix_unified_calculation()
    test2_result = test_config_consistency()
    
    # 显示使用示例
    show_usage_examples()
    
    # 总结
    print('📊 测试总结')
    print('=' * 50)
    print(f'TRIX计算测试: {"✅ 通过" if test1_result else "❌ 失败"}')
    print(f'配置一致性测试: {"✅ 通过" if test2_result else "❌ 失败"}')
    
    if test1_result and test2_result:
        print('\n🎉 所有测试通过！TRIX统一配置工作正常')
        print('💡 现在可以通过修改config.py中的TRIX_EMA_PERIOD来统一控制所有TRIX计算')
    else:
        print('\n⚠️ 部分测试失败，请检查配置和代码')

if __name__ == '__main__':
    main()
