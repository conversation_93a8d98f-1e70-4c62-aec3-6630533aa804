# coding=utf-8
from __future__ import print_function, absolute_import, unicode_literals
from gm.api import *
import datetime
import numpy as np
import pandas as pd
import talib
import time
import sqlite3
from smart_scoring_system import get_scoring_system

# 增强指标集成模块
try:
    from enhanced_buy_integration import EnhancedBuyIntegration
    ENHANCED_INDICATORS_AVAILABLE = True
    print("✅ 增强指标模块导入成功")
except ImportError as e:
    ENHANCED_INDICATORS_AVAILABLE = False
    print(f"⚠️ 增强指标模块导入失败: {e}")

# 完整增强因子系统集成模块 (192个因子)
try:
    from 完整增强因子系统 import CompleteEnhancedFactorSystem
    from enhanced_factors_config import get_buy_threshold, get_position_config, get_risk_config
    ENHANCED_FACTORS_AVAILABLE = True
    print("✅ 完整增强因子系统导入成功 (192个因子)")
except ImportError as e:
    # 备用方案：使用原有系统
    try:
        from backtest_enhanced_integration import BacktestEnhancedIntegration
        from enhanced_factors_config import get_buy_threshold, get_position_config, get_risk_config
        ENHANCED_FACTORS_AVAILABLE = True
        print("✅ 备用增强因子系统导入成功 (52个因子)")
    except ImportError as e2:
        ENHANCED_FACTORS_AVAILABLE = False
        print(f"⚠️ 增强因子系统导入失败: {e}, {e2}")

import time
import os
import csv
import logging  # 添加 logging 模块导入
import argparse
import importlib
import json
from collections import defaultdict
from functools import wraps

# 导入自定义模块
from scripts.data_manager import DataManager, get_data_manager, save_trade, save_analysis

# =============================================================================
# 全局数据缓存管理器 - 性能优化核心组件
# =============================================================================

class GlobalDataCacheManager:
    """全局数据缓存管理器 - 解决数据获取效率问题"""

    def __init__(self, context):
        self.context = context
        self.cache = {}  # 主缓存
        self.cache_timestamps = {}  # 缓存时间戳
        self.cache_expire_seconds = 3600  # 缓存过期时间（1小时）
        self.batch_cache = {}  # 批量数据缓存
        self.today_str = context.now.strftime('%Y-%m-%d')

        # 性能统计
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'batch_requests': 0,
            'single_requests': 0
        }

    def get_cache_key(self, symbol, count, frequency='1d', fields=None):
        """生成缓存键"""
        fields_str = '_'.join(sorted(fields)) if fields else 'all'
        return f"{symbol}_{count}_{frequency}_{fields_str}_{self.today_str}"

    def is_cache_valid(self, cache_key):
        """检查缓存是否有效"""
        if cache_key not in self.cache_timestamps:
            return False

        cache_time = self.cache_timestamps[cache_key]
        current_time = time.time()
        return (current_time - cache_time) < self.cache_expire_seconds

    def get_cached_data(self, symbol, count, frequency='1d', fields=None):
        """获取缓存的数据"""
        cache_key = self.get_cache_key(symbol, count, frequency, fields)

        if cache_key in self.cache and self.is_cache_valid(cache_key):
            self.stats['cache_hits'] += 1
            return self.cache[cache_key]

        self.stats['cache_misses'] += 1
        return None

    def cache_data(self, symbol, count, frequency, fields, data):
        """缓存数据"""
        cache_key = self.get_cache_key(symbol, count, frequency, fields)
        self.cache[cache_key] = data
        self.cache_timestamps[cache_key] = time.time()

    def batch_preload_data(self, symbols, count=60, frequency='1d', fields=None):
        """批量预加载数据"""
        if not symbols:
            return {}

        self.stats['batch_requests'] += 1

        # 检查哪些数据需要获取
        missing_symbols = []
        cached_data = {}

        for symbol in symbols:
            cached = self.get_cached_data(symbol, count, frequency, fields)
            if cached is not None:
                cached_data[symbol] = cached
            else:
                missing_symbols.append(symbol)

        # 批量获取缺失的数据
        if missing_symbols:
            try:
                # 使用批量API获取数据
                batch_data = history_n(
                    symbol=missing_symbols,
                    count=count,
                    frequency=frequency,
                    fields=fields or ['open', 'high', 'low', 'close', 'volume'],
                    end_time=self.context.now
                )

                if isinstance(batch_data, dict):
                    # 缓存批量获取的数据
                    for symbol, data in batch_data.items():
                        if data is not None:
                            self.cache_data(symbol, count, frequency, fields, data)
                            cached_data[symbol] = data
                else:
                    # 批量获取失败，回退到单个获取
                    for symbol in missing_symbols:
                        try:
                            data = history_n(
                                symbol=symbol,
                                count=count,
                                frequency=frequency,
                                fields=fields or ['open', 'high', 'low', 'close', 'volume'],
                                end_time=self.context.now
                            )
                            if data is not None:
                                self.cache_data(symbol, count, frequency, fields, data)
                                cached_data[symbol] = data
                        except:
                            continue

            except Exception as e:
                # 批量获取异常，回退到单个获取
                for symbol in missing_symbols:
                    try:
                        data = history_n(
                            symbol=symbol,
                            count=count,
                            frequency=frequency,
                            fields=fields or ['open', 'high', 'low', 'close', 'volume'],
                            end_time=self.context.now
                        )
                        if data is not None:
                            self.cache_data(symbol, count, frequency, fields, data)
                            cached_data[symbol] = data
                    except:
                        continue

        return cached_data

    def clear_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        for cache_key, cache_time in self.cache_timestamps.items():
            if (current_time - cache_time) >= self.cache_expire_seconds:
                expired_keys.append(cache_key)

        for key in expired_keys:
            if key in self.cache:
                del self.cache[key]
            if key in self.cache_timestamps:
                del self.cache_timestamps[key]

        if expired_keys:
            self.context.log.debug(f"清理了 {len(expired_keys)} 个过期缓存项")

    def get_stats(self):
        """获取缓存统计信息"""
        total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
        hit_rate = (self.stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_hit_rate': hit_rate,
            'total_cached_items': len(self.cache),
            **self.stats
        }

# =============================================================================
# 时间戳缓存管理器 - 优化日志系统性能
# =============================================================================

class TimestampCacheManager:
    """时间戳缓存管理器 - 减少重复的时间格式化操作"""

    def __init__(self, context):
        self.context = context
        self.cached_timestamp = None
        self.cached_date = None
        self.last_update_time = None
        self.update_interval = 1  # 每秒更新一次

    def get_timestamp(self):
        """获取缓存的时间戳字符串"""
        current_time = time.time()

        # 如果缓存为空或超过更新间隔，则更新缓存
        if (self.last_update_time is None or
            (current_time - self.last_update_time) >= self.update_interval):

            self.cached_timestamp = self.context.now.strftime('%Y-%m-%d %H:%M:%S')
            self.cached_date = self.context.now.strftime('%Y-%m-%d')
            self.last_update_time = current_time

        return self.cached_timestamp

    def get_date(self):
        """获取缓存的日期字符串"""
        if self.cached_date is None:
            self.get_timestamp()  # 这会同时更新日期缓存
        return self.cached_date

# =============================================================================
# TRIX计算缓存管理器 - 优化技术指标计算
# =============================================================================

class TrixCacheManager:
    """TRIX计算缓存管理器 - 避免重复计算"""

    def __init__(self, context):
        self.context = context
        self.trix_cache = {}
        self.cache_timestamps = {}
        self.cache_expire_seconds = 1800  # 30分钟过期
        self.today_str = context.now.strftime('%Y-%m-%d')

    def get_trix_cache_key(self, symbol, period=None):
        """生成TRIX缓存键"""
        if period is None:
            period = get_config_value('TRIX_EMA_PERIOD', 3)
        return f"trix_{symbol}_{period}_{self.today_str}"

    def get_cached_trix(self, symbol, period=None):
        """获取缓存的TRIX值"""
        cache_key = self.get_trix_cache_key(symbol, period)

        if cache_key in self.trix_cache:
            cache_time = self.cache_timestamps.get(cache_key, 0)
            if (time.time() - cache_time) < self.cache_expire_seconds:
                return self.trix_cache[cache_key]

        return None

    def cache_trix(self, symbol, trix_values, period=None):
        """缓存TRIX计算结果"""
        cache_key = self.get_trix_cache_key(symbol, period)
        self.trix_cache[cache_key] = trix_values
        self.cache_timestamps[cache_key] = time.time()

    def clear_expired_cache(self):
        """清理过期的TRIX缓存"""
        current_time = time.time()
        expired_keys = []

        for cache_key, cache_time in self.cache_timestamps.items():
            if (current_time - cache_time) >= self.cache_expire_seconds:
                expired_keys.append(cache_key)

        for key in expired_keys:
            if key in self.trix_cache:
                del self.trix_cache[key]
            if key in self.cache_timestamps:
                del self.cache_timestamps[key]

# =============================================================================
# 批量数据库操作管理器 - 优化数据库写入性能
# =============================================================================

class BatchDatabaseManager:
    """批量数据库操作管理器 - 减少数据库IO次数"""

    def __init__(self, context):
        self.context = context
        self.pending_records = []
        self.batch_size = 50  # 批量写入大小
        self.last_flush_time = time.time()
        self.flush_interval = 30  # 30秒强制刷新一次

    def add_record(self, record):
        """添加记录到批量队列"""
        self.pending_records.append(record)

        # 检查是否需要批量写入
        if len(self.pending_records) >= self.batch_size:
            self.flush_records()

    def flush_records(self):
        """批量写入所有待处理记录"""
        if not self.pending_records:
            return

        try:
            # 批量保存到数据库
            for record in self.pending_records:
                save_analysis(record)

            self.context.log.debug(f"批量写入 {len(self.pending_records)} 条记录到数据库")
            self.pending_records.clear()
            self.last_flush_time = time.time()

        except Exception as e:
            self.context.log.error(f"批量数据库写入失败: {e}")

    def check_and_flush(self):
        """检查并在必要时刷新记录"""
        current_time = time.time()
        if (current_time - self.last_flush_time) >= self.flush_interval:
            self.flush_records()

# =============================================================================
# 性能优化辅助函数
# =============================================================================

def get_cached_timestamp(context):
    """获取缓存的时间戳，避免重复格式化"""
    if hasattr(context, 'timestamp_cache') and context.timestamp_cache:
        return context.timestamp_cache.get_timestamp()
    else:
        return context.now.strftime('%Y-%m-%d %H:%M:%S')

def get_cached_date(context):
    """获取缓存的日期，避免重复格式化"""
    if hasattr(context, 'timestamp_cache') and context.timestamp_cache:
        return context.timestamp_cache.get_date()
    else:
        return context.now.strftime('%Y-%m-%d')

def log_with_cached_timestamp(context, level, message):
    """使用缓存时间戳的日志函数"""
    timestamp = get_cached_timestamp(context)
    full_message = f"{timestamp} - {message}"

    if level == 'info':
        context.log.info(full_message)
    elif level == 'debug':
        context.log.debug(full_message)
    elif level == 'warning':
        context.log.warning(full_message)
    elif level == 'error':
        context.log.error(full_message)
    else:
        context.log.info(full_message)

def log_info(context, message):
    """优化的info日志函数"""
    context.log.info(f"{get_cached_timestamp(context)} - {message}")

def log_error(context, message):
    """优化的error日志函数"""
    context.log.error(f"{get_cached_timestamp(context)} - {message}")

def log_warning(context, message):
    """优化的warning日志函数"""
    context.log.warning(f"{get_cached_timestamp(context)} - {message}")

def log_debug(context, message):
    """优化的debug日志函数"""
    context.log.debug(f"{get_cached_timestamp(context)} - {message}")
from position_manager import PositionManager  # 修改为直接导入
from config_manager import ConfigManager, config_manager, get_param
from data_fetcher import DataFetcher
from signal_calculator import SignalCalculator
from signal_generator import SignalGenerator
from trade_executor import TradeExecutor
from risk_manager import RiskManager
from history_data_manager import HistoryDataManager
from volatility_calculator import VolatilityCalculator
# 导入日志管理器
from logger_manager import setup_logger, get_strategy_logger
# 导入性能分析器
from performance_profiler import init_profiler, profile
# 导入性能优化器
from performance_optimizer import get_performance_optimizer
# 导入TRIX预筛选器
from trix_prefilter import get_trix_prefilter
# 导入数据库初始化器
from database_initializer import DatabaseInitializer

# 导入静态配置（用于非动态配置模式）
try:
    import config as static_config
except ImportError:
    static_config = None

# 全局变量用于存储是否使用动态配置
_USE_DYNAMIC_CONFIG = os.environ.get('USE_DYNAMIC_CONFIG', '0') == '1'

def get_config_value(param_name, default=None):
    """
    统一的配置值获取方法，支持动态配置和静态配置
    
    参数:
    - param_name: 参数名称
    - default: 默认值，如果参数不存在则返回此值
    
    返回:
    - 参数值
    """
    global _USE_DYNAMIC_CONFIG
    
    # 创建一个简单的日志记录，在初始化前使用print
    log_func = print if 'logging' not in globals() else logging.info
    
    try:
        if _USE_DYNAMIC_CONFIG:
            # 使用动态配置管理器获取参数
            try:
                # 确保get_param函数存在且可调用
                if 'get_param' in globals() and callable(get_param):
                    value = get_param(param_name, default)
                else:
                    log_func(f"动态配置函数get_param不可用，使用默认值: {default}")
                    return default
            except Exception as e:
                log_func(f"动态配置获取参数异常 {param_name}: {str(e)}")
                return default
                
            # 对于重要参数，添加调试日志
            if param_name in ['BUY_CHECK_INTERVAL_MODE', 'BUY_CHECK_INTERVAL_MINUTES', 'MEMORY_LIMIT_MB']:
                log_func(f"从动态配置获取参数 {param_name} = {value} (默认值: {default})")
            return value
        else:
            # 使用静态导入的配置模块获取参数
            try:
                if static_config is not None and hasattr(static_config, param_name):
                    value = getattr(static_config, param_name)
                    # 对于重要参数，添加调试日志
                    if param_name in ['BUY_CHECK_INTERVAL_MODE', 'BUY_CHECK_INTERVAL_MINUTES', 'MEMORY_LIMIT_MB']:
                        log_func(f"从静态配置获取参数 {param_name} = {value}")
                    return value
            except Exception as e:
                log_func(f"静态配置获取参数异常 {param_name}: {str(e)}")
                return default
            
            # 对于重要参数，添加调试日志
            if param_name in ['BUY_CHECK_INTERVAL_MODE', 'BUY_CHECK_INTERVAL_MINUTES', 'MEMORY_LIMIT_MB']:
                log_func(f"未找到参数 {param_name}，使用默认值: {default}")
            return default
    except Exception as e:
        log_func(f"获取配置值异常 {param_name}: {str(e)}")
        return default

# 初始化数据管理器
data_manager = get_data_manager()

# =============================================================================
# 数据完整性和未来函数保护功能
# =============================================================================

def is_intraday_data_allowed(context):
    """检查是否允许使用当日数据"""
    try:
        # 获取配置
        cutoff_time_str = get_config_value('DATA_INTEGRITY_CONFIG', {}).get('intraday_volume_cutoff_time', '14:30')
        cutoff_hour, cutoff_minute = map(int, cutoff_time_str.split(':'))
        cutoff_time = datetime.time(cutoff_hour, cutoff_minute)

        current_time = context.now.time()
        return current_time >= cutoff_time
    except Exception as e:
        context.log.error(f"检查当日数据使用权限失败: {e}")
        return False

def filter_future_function_fields(data_dict, context):
    """过滤可能包含未来函数的字段"""
    try:
        if not get_config_value('DATA_INTEGRITY_CONFIG', {}).get('enable_future_function_protection', True):
            return data_dict

        allow_intraday = is_intraday_data_allowed(context)
        if allow_intraday:
            return data_dict

        # 获取风险字段列表
        risk_fields = get_config_value('FUTURE_FUNCTION_RISK_FIELDS', [])

        filtered_data = {}
        filtered_count = 0

        for key, value in data_dict.items():
            # 检查字段名是否包含风险关键词
            is_risk_field = any(risk_field.lower() in key.lower() for risk_field in risk_fields)

            if is_risk_field:
                filtered_data[key] = None
                filtered_count += 1
            else:
                filtered_data[key] = value

        if filtered_count > 0:
            context.log.info(f"🚨 未来函数保护：过滤了 {filtered_count} 个风险字段")

        return filtered_data

    except Exception as e:
        context.log.error(f"过滤未来函数字段失败: {e}")
        return data_dict

def add_data_integrity_markers(record, context):
    """添加数据完整性标记"""
    try:
        current_time = context.now.time()
        allow_intraday = is_intraday_data_allowed(context)

        record.update({
            'future_function_protected': 1 if not allow_intraday else 0,
            'data_collection_time': current_time.strftime('%H:%M:%S'),
            'intraday_data_allowed': 1 if allow_intraday else 0,
            'data_integrity_version': '1.0'
        })

        return record

    except Exception as e:
        context.log.error(f"添加数据完整性标记失败: {e}")
        return record

def clean_existing_future_function_data():
    """清理现有数据库中的未来函数数据"""
    try:
        db_path = 'data/trades.db'
        conn = sqlite3.connect(db_path)

        # 获取风险字段
        risk_fields = get_config_value('FUTURE_FUNCTION_RISK_FIELDS', [])

        # 对于收盘前的记录，将风险字段设为NULL
        for field in risk_fields:
            try:
                update_query = f'''
                    UPDATE trades
                    SET {field} = NULL
                    WHERE strftime('%H:%M', timestamp) < '14:30'
                    AND {field} IS NOT NULL
                    AND action = 'BUY'
                '''
                cursor = conn.execute(update_query)
                affected_rows = cursor.rowcount
                if affected_rows > 0:
                    print(f"🔧 清理字段 {field}: {affected_rows} 条记录")
            except Exception as e:
                print(f"⚠️ 清理字段 {field} 失败: {e}")

        # 添加清理标记
        try:
            conn.execute('ALTER TABLE trades ADD COLUMN future_function_cleaned INTEGER DEFAULT 0')
        except:
            pass

        conn.execute('''
            UPDATE trades
            SET future_function_cleaned = 1
            WHERE strftime('%H:%M', timestamp) < '14:30'
            AND action = 'BUY'
        ''')

        conn.commit()
        conn.close()

        print("✅ 未来函数数据清理完成")
        return True

    except Exception as e:
        print(f"❌ 清理未来函数数据失败: {e}")
        return False

def prepare_scoring_data(symbol, data, context):
    """
    为智能评分系统准备数据

    参数:
    - symbol: 股票代码
    - data: 历史数据DataFrame
    - context: 策略上下文

    返回:
    - scoring_data: 评分系统所需的数据字典
    """
    try:
        scoring_data = {'symbol': symbol}

        if data is None or len(data) < 20:
            return scoring_data

        # 获取价格数据
        close_prices = data['close'].values
        high_prices = data['high'].values
        low_prices = data['low'].values
        volume_data = data['volume'].values

        current_price = close_prices[-1]
        prev_price = close_prices[-2] if len(close_prices) > 1 else current_price

        # 1. 计算价格变化百分比
        if prev_price > 0:
            scoring_data['price_change_pct'] = (current_price - prev_price) / prev_price * 100
        else:
            scoring_data['price_change_pct'] = 0

        # 2. 计算均线距离
        try:
            import talib

            # 20日均线
            if len(close_prices) >= 20:
                ma20 = talib.SMA(close_prices, timeperiod=20)
                if len(ma20) > 0 and ma20[-1] > 0:
                    scoring_data['ma20_distance_pct'] = (current_price - ma20[-1]) / ma20[-1] * 100
                else:
                    scoring_data['ma20_distance_pct'] = 0
            else:
                scoring_data['ma20_distance_pct'] = 0

            # 10日均线
            if len(close_prices) >= 10:
                ma10 = talib.SMA(close_prices, timeperiod=10)
                if len(ma10) > 0 and ma10[-1] > 0:
                    scoring_data['ma10_distance_pct'] = (current_price - ma10[-1]) / ma10[-1] * 100
                else:
                    scoring_data['ma10_distance_pct'] = 0
            else:
                scoring_data['ma10_distance_pct'] = 0

            # 5日均线
            if len(close_prices) >= 5:
                ma5 = talib.SMA(close_prices, timeperiod=5)
                if len(ma5) > 0 and ma5[-1] > 0:
                    scoring_data['ma5_distance_pct'] = (current_price - ma5[-1]) / ma5[-1] * 100
                else:
                    scoring_data['ma5_distance_pct'] = 0
            else:
                scoring_data['ma5_distance_pct'] = 0

        except ImportError:
            # 简化计算
            if len(close_prices) >= 20:
                ma20 = np.mean(close_prices[-20:])
                scoring_data['ma20_distance_pct'] = (current_price - ma20) / ma20 * 100 if ma20 > 0 else 0
            else:
                scoring_data['ma20_distance_pct'] = 0

        # 3. 计算ATR标准化波动率
        try:
            import talib
            if len(close_prices) >= 14:
                atr = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)
                if len(atr) > 0 and atr[-1] > 0 and current_price > 0:
                    scoring_data['atr_normalized'] = atr[-1] / current_price * 100
                else:
                    scoring_data['atr_normalized'] = 0
            else:
                scoring_data['atr_normalized'] = 0
        except ImportError:
            # 简化计算
            if len(close_prices) >= 14:
                high_low_range = np.mean(high_prices[-14:] - low_prices[-14:])
                scoring_data['atr_normalized'] = high_low_range / current_price * 100 if current_price > 0 else 0
            else:
                scoring_data['atr_normalized'] = 0

        # 4. 计算RSI
        try:
            import talib
            if len(close_prices) >= 14:
                rsi = talib.RSI(close_prices, timeperiod=14)
                scoring_data['rsi_5d'] = rsi[-1] if len(rsi) > 0 else 50
            else:
                scoring_data['rsi_5d'] = 50
        except ImportError:
            scoring_data['rsi_5d'] = 50

        # 5. 计算布林带位置
        try:
            import talib
            if len(close_prices) >= 20:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close_prices, timeperiod=20)
                if len(bb_upper) > 0 and bb_upper[-1] > bb_lower[-1]:
                    bb_position = (current_price - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) * 100
                    scoring_data['bb_position_20'] = bb_position
                else:
                    scoring_data['bb_position_20'] = 50
            else:
                scoring_data['bb_position_20'] = 50
        except ImportError:
            scoring_data['bb_position_20'] = 50

        # 6. 计算价格动量
        if len(close_prices) >= 3:
            price_3d_ago = close_prices[-4] if len(close_prices) > 3 else close_prices[0]
            if price_3d_ago > 0:
                scoring_data['price_momentum_3d'] = (current_price - price_3d_ago) / price_3d_ago * 100
            else:
                scoring_data['price_momentum_3d'] = 0
        else:
            scoring_data['price_momentum_3d'] = 0

        return scoring_data

    except Exception as e:
        if context:
            context.log.error(f"准备评分数据失败 {symbol}: {e}")
        return {'symbol': symbol}

def get_buy_score_from_db(symbol, buy_date):
    """
    从数据库获取买入时的智能评分

    参数:
    - symbol: 股票代码
    - buy_date: 买入日期

    返回:
    - score: 买入时的评分，如果未找到返回None
    """
    try:
        db_path = 'data/trades.db'
        conn = sqlite3.connect(db_path)

        # 查询买入记录的智能评分
        query = '''
            SELECT smart_score
            FROM trades
            WHERE symbol = ?
            AND action = 'BUY'
            AND DATE(timestamp) = DATE(?)
            AND smart_score IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 1
        '''

        cursor = conn.execute(query, (symbol, buy_date))
        result = cursor.fetchone()
        conn.close()

        if result:
            return result[0]
        else:
            return None

    except Exception as e:
        print(f"获取买入评分失败 {symbol}: {e}")
        return None

# CSV Fieldnames - 保留这些定义以便向后兼容
ANALYSIS_FIELDNAMES = [
    'Timestamp', 'Symbol', 'Current_Price', 'MA3', 'MA7', 'MA20',
    'RSI', 'MACD', 'KDJ_K', 'KDJ_D', 'Boll_Middle',
    'Volume_Ratio', 'Trend_Strength', 'MA_Cross_Buy_Signal'
]
TRADE_FIELDNAMES = [
    'Timestamp', 'Symbol', 'Action', 'Price', 'Volume',
    'Trend_Strength_Buy', 'MA3_Buy', 'MA7_Buy', 'MA_Cross_Buy_Signal_Buy',
    'Sell_Reason', 'Cost_Price_Sell', 'Net_Profit_Pct_Sell', 'Confirmed_High_Sell',
    'Confirmed_High_Time', 'Holding_Hours', 'Max_Profit_Pct', 'Final_Drawdown_Pct',
    'TRIX_Buy', 'Volatility', 'ATR_Pct', 'Volatility_Score', 'Allocation_Factor',
    'ADX', 'DMI_Plus', 'DMI_Minus', 'BB_Width', 'BB_Position',
    'RSI', 'CCI', 'MACD', 'MACD_Signal', 'MACD_Hist',
    'Relative_Volume', 'Volume_Change_Rate', 'OBV',
    'Industry_Relative_Strength', 'Market_Correlation',
    'PE_Ratio', 'PB_Ratio', 'ROE',
    'Distance_From_High', 'MA_System_Status'
]

def init_csv_files():
    """初始化数据库 - 不再使用CSV文件"""
    # 只初始化数据库
    if data_manager.use_db:
        data_manager._init_db()

def append_to_csv(filepath, data_row_dict, fieldnames):
    """将数据保存到数据库 - 不再使用CSV"""
    if 'trade_log.csv' in filepath:
        return save_trade(data_row_dict)
    elif 'analysis_log.csv' in filepath:
        return save_analysis(data_row_dict)
    else:
        # 向后兼容，但实际上不再写入CSV
        return True

def refresh_subscriptions(context):
    """
    刷新订阅列表
    
    修改说明:
    1. 确保在实盘/模拟盘模式下使用优化的批次订阅
    2. 优先订阅持仓股票
    3. 避免不必要的全量订阅尝试
    4. 在强制回测模式下避免重复订阅
    5. 支持BACKTEST_SUBSCRIBE_ONCE参数，回测模式下只订阅一次
    6. 支持自定义订阅股票列表
    """
    try:
        log_info(context, "开始刷新订阅列表")

        # 检查是否在交易时间段或回测模式
        if not (is_trading_hour(context.now) or context.run_mode == 'backtest'):
            log_info(context, "当前不在交易时间段，跳过刷新订阅操作")
            return

        # 检查是否有自定义订阅列表
        if hasattr(context, 'custom_symbols') and context.custom_symbols:
            log_info(context, f"使用自定义订阅列表: {context.custom_symbols}")
            # 订阅自定义股票列表
            try:
                # 使用data_fetcher的方法订阅股票
                context.data_fetcher._subscribe_batch(context.custom_symbols)
                log_info(context, "成功订阅自定义股票列表")
            except Exception as e:
                log_error(context, f"订阅自定义股票列表异常: {str(e)}")
            # 更新订阅状态
            context.subscribed_today = True
            context.last_subscription_date = context.now.date()
            context.subscription_errors = 0
            context.subscription_error_cooldown = False
            return
        
        # 检查是否启用了自定义订阅模式但没有设置股票列表
        custom_subscribe_mode = get_config_value('CUSTOM_SUBSCRIBE_MODE', False)
        if custom_subscribe_mode:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已启用自定义订阅模式但未设置股票列表，不进行任何订阅")
            # 更新订阅状态
            context.subscribed_today = True
            context.last_subscription_date = context.now.date()
            context.subscription_errors = 0
            context.subscription_error_cooldown = False
            return
        
        # 检查回测模式下是否启用了只订阅一次的功能
        if context.run_mode == 'backtest':
            backtest_subscribe_once = get_config_value('BACKTEST_SUBSCRIBE_ONCE', True)
            if backtest_subscribe_once and hasattr(context, 'subscribed_today') and context.subscribed_today:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式已启用BACKTEST_SUBSCRIBE_ONCE，跳过重复订阅")
                return
        
        # 检查是否是强制回测模式（ENABLE_MODE_ADAPTIVE=1）且已经订阅过
        mode_adaptive_setting = get_config_value('ENABLE_MODE_ADAPTIVE', 0)
        if mode_adaptive_setting == 1:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前为强制回测模式(ENABLE_MODE_ADAPTIVE=1)")
            if hasattr(context, 'subscribed_today') and context.subscribed_today:
                if hasattr(context, 'last_subscription_date') and context.last_subscription_date == context.now.date():
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下今日已订阅过，跳过重复订阅")
                    return
                else:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下上次订阅日期({context.last_subscription_date})与今日不同，执行今日订阅")
            else:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下首次订阅")
        
        # 检查今天是否已经订阅过
        if hasattr(context, 'subscribed_today') and context.subscribed_today:
            today = context.now.date()
            if hasattr(context, 'last_subscription_date') and context.last_subscription_date == today:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 今天已经订阅过，跳过重复订阅")
                return
        
        # 检查是否在订阅错误冷却期
        if hasattr(context, 'subscription_error_cooldown') and context.subscription_error_cooldown:
            if hasattr(context, 'last_subscription_error_time'):
                cooldown_minutes = 2  # 冷却时间2分钟
                elapsed_minutes = (context.now - context.last_subscription_error_time).total_seconds() / 60
                if elapsed_minutes < cooldown_minutes:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅错误冷却期内，还需等待 {cooldown_minutes - elapsed_minutes:.1f} 分钟")
                    return
                else:
                    context.subscription_error_cooldown = False
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅错误冷却期已结束，可以重新尝试订阅")
        
        # 实盘/模拟盘模式下，使用优化的批次订阅
        if context.run_mode == 'live':
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘模式，使用优化的批次订阅")
            
            # 确保持仓股票被优先订阅
            if hasattr(context, 'positions') and context.positions:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 确保持仓股票优先订阅")
                context.data_fetcher.current_subscribed_batch = list(context.positions.keys())
            
            # 根据配置决定是订阅全市场还是指数成分股
            if get_config_value('SUBSCRIBE_ALL_MARKET', False):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅全市场股票")
                context.data_fetcher.subscribe_constituents('ALL')
            else:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅指数 {context.index_symbol} 成分股")
                context.data_fetcher.subscribe_constituents(context.index_symbol)
            
            # 更新订阅状态
            context.subscribed_today = True
            context.last_subscription_date = context.now.date()
            context.subscription_errors = 0
            context.subscription_error_cooldown = False
            return
        
        # 回测模式下，根据配置决定是全量订阅还是批次订阅
        try:
            # 根据配置决定是订阅全市场还是指数成分股
            if get_config_value('SUBSCRIBE_ALL_MARKET', False):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅全市场股票")
                context.data_fetcher.subscribe_constituents('ALL')
            else:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅指数 {context.index_symbol} 成分股")
                context.data_fetcher.subscribe_constituents(context.index_symbol)
            
            # 更新订阅状态
            context.subscribed_today = True
            context.last_subscription_date = context.now.date()
            context.subscription_errors = 0
            context.subscription_error_cooldown = False
            
        except Exception as e:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 刷新订阅异常: {str(e)}")
            
            # 记录订阅错误
            if hasattr(context, 'subscription_errors'):
                context.subscription_errors += 1
                
                # 如果连续错误超过阈值，进入冷却期
                if context.subscription_errors >= 3:
                    context.subscription_error_cooldown = True
                    context.last_subscription_error_time = context.now
                    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 连续订阅错误达到阈值，进入冷却期")
                    
                    # 尝试只订阅持仓股票
                    try:
                        _subscribe_holdings_only(context)
                    except Exception as e2:
                        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试只订阅持仓股票也失败: {str(e2)}")
            
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 刷新订阅函数异常: {str(e)}")

def position_summary(context):
    """定期输出持仓摘要"""
    try:
        # 记录执行开始时间(用于性能分析)
        start_time = time.time()
        
        # 检查持仓摘要功能是否启用
        enable_position_summary = get_config_value('ENABLE_POSITION_SUMMARY', True)
        if not enable_position_summary:
            return  # 如果禁用了持仓摘要功能，直接返回
        
        # 获取当前持仓
        positions = context.account().positions()
        
        # 检查是否在无持仓时输出摘要
        position_summary_for_empty = get_config_value('POSITION_SUMMARY_FOR_EMPTY', True)
        if not positions and not position_summary_for_empty:
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前无持仓，跳过持仓摘要输出")
            return
            
        # 获取摘要详细程度
        detail_level = get_config_value('POSITION_SUMMARY_DETAIL_LEVEL', 'normal')
            
        if not positions:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 定期持仓摘要: 当前无持仓")
            # 如果配置为详细模式，还可以输出额外的账户信息
            if detail_level == 'detail':
                account_info = context.account()
                context.log.info(f"""
                账户信息:
                可用资金: {account_info.cash.available:.2f}
                总资产: {account_info.cash.nav:.2f}
                """)
            return
            
        # 计算持仓总市值和盈亏情况
        total_value = 0
        total_cost = 0
        profit_positions = 0
        loss_positions = 0
        
        for position in positions:
            symbol = position['symbol']
            current_price = position['price']
            volume = position['volume']
            market_value = current_price * volume
            total_value += market_value
            
            # 获取成本信息
            if symbol in context.positions_cost:
                cost_info = context.positions_cost[symbol]
                cost_price = cost_info['cost_price']
                position_cost = cost_price * volume
                total_cost += position_cost
                
                # 统计盈亏情况
                if current_price > cost_price:
                    profit_positions += 1
                else:
                    loss_positions += 1
        
        # 计算总盈亏
        if total_cost > 0:
            total_profit_pct = (total_value - total_cost) / total_cost * 100
        else:
            total_profit_pct = 0
        
        # 准备总成本和总盈亏的显示值
        total_cost_str = f"{total_cost:.2f}" if total_cost > 0 else "未知"
        total_profit_str = f"{(total_value - total_cost):.2f}" if total_cost > 0 else "未知"
        
        # 根据详细程度选择输出内容
        if detail_level == 'simple':
            # 简单模式: 只输出基本信息
            context.log.info(f"""
            {context.now.strftime('%Y-%m-%d %H:%M:%S')} - 定期持仓摘要(简洁模式):
            总持仓数: {len(positions)}
            总市值: {total_value:.2f}
            当前现金: {context.account().cash.available:.2f}
            """)
        elif detail_level == 'detail':
            # 详细模式: 输出完整信息，包括每个持仓的详情
            position_details = []
            for position in positions:
                try:
                    symbol = position['symbol']
                    current_price = position['price']
                    volume = position['volume']
                    market_value = current_price * volume
                    
                    if symbol in context.positions_cost:
                        cost_info = context.positions_cost[symbol]
                        cost_price = cost_info['cost_price']
                        buy_date = cost_info.get('buy_date', '未知')
                        profit_pct = (current_price - cost_price) / cost_price * 100
                        position_details.append(f"{symbol}: 持仓量{volume}, 现价{current_price:.2f}, 成本{cost_price:.2f}, 盈亏{profit_pct:.2f}%, 买入日期{buy_date}")
                except Exception as e:
                    context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 处理持仓详情异常 - {symbol if 'symbol' in locals() else '未知'}: {str(e)}")
            
            context.log.info(f"""
            {context.now.strftime('%Y-%m-%d %H:%M:%S')} - 定期持仓摘要(详细模式):
            总持仓数: {len(positions)}
            总市值: {total_value:.2f}
            总成本: {total_cost_str}
            总盈亏: {total_profit_str} ({total_profit_pct:.2f}%)
            盈利股票数: {profit_positions}
            亏损股票数: {loss_positions}
            当前现金: {context.account().cash.available:.2f}
            总资产: {context.account().cash.nav:.2f}
            
            持仓详情:
            {chr(10).join(position_details)}
            """)
        else:
            # 普通模式: 输出一般信息
            context.log.info(f"""
            {context.now.strftime('%Y-%m-%d %H:%M:%S')} - 定期持仓摘要:
            总持仓数: {len(positions)}
            总市值: {total_value:.2f}
            总成本: {total_cost_str}
            总盈亏: {total_profit_str} ({total_profit_pct:.2f}%)
            盈利股票数: {profit_positions}
            亏损股票数: {loss_positions}
            当前现金: {context.account().cash.available:.2f}
            """)
        
        # 记录执行结束时间和耗时(用于性能分析)
        end_time = time.time()
        elapsed_ms = (end_time - start_time) * 1000
        if elapsed_ms > 100:  # 如果耗时超过100毫秒，记录警告
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓摘要生成耗时较长: {elapsed_ms:.2f}ms")
            
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓摘要生成异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

def is_last_trading_day_of_period(context, period='week'):
    """
    判断当前日期是否为每周或每月的最后一个交易日
    
    参数:
    context: 策略上下文
    period: 'week' 或 'month'，判断是每周还是每月的最后交易日
    
    返回:
    bool: 是否为指定周期的最后交易日
    """
    try:
        current_date = context.now.date()
        
        # 获取当前日期所在周或月的下一个日期
        if period == 'week':
            # 获取下一个周一
            days_to_next_monday = 7 - current_date.weekday()
            if days_to_next_monday == 7:  # 如果今天是周一，则设为7天后
                days_to_next_monday = 7
            next_period_start = current_date + datetime.timedelta(days=days_to_next_monday)
        elif period == 'month':
            # 获取下个月的第一天
            if current_date.month == 12:
                next_period_start = datetime.date(current_date.year + 1, 1, 1)
            else:
                next_period_start = datetime.date(current_date.year, current_date.month + 1, 1)
        else:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 不支持的周期类型: {period}")
            return False
        
        # 获取当前日期到下一周期开始的所有日期
        dates_to_check = []
        check_date = current_date
        while check_date < next_period_start:
            dates_to_check.append(check_date)
            check_date = check_date + datetime.timedelta(days=1)
        
        # 过滤出交易日
        trading_dates = []
        for date in dates_to_check:
            # 周末不是交易日
            if date.weekday() >= 5:  # 5是周六，6是周日
                continue
                
            # 这里可以添加节假日判断逻辑，如果有节假日数据
            # 简化处理，只排除周末
            trading_dates.append(date)
        
        # 判断当前日期是否为过滤后的最后一个交易日
        if not trading_dates:
            return False
            
        return current_date == trading_dates[-1]
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 判断{period}最后交易日异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
        return False

def initialize_enhanced_indicators(context):
    """初始化增强指标集成器"""
    # 初始化原有增强指标集成器
    if ENHANCED_INDICATORS_AVAILABLE:
        try:
            context.enhanced_buy_integration = EnhancedBuyIntegration(context)
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 增强买入指标集成器已初始化")
        except Exception as e:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 增强指标集成器初始化失败: {e}")
            context.enhanced_buy_integration = None
    else:
        context.enhanced_buy_integration = None
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 增强指标模块不可用，使用基础指标")

    # 初始化完整增强因子系统 (192个因子)
    if ENHANCED_FACTORS_AVAILABLE:
        try:
            # 优先使用完整增强因子系统
            try:
                context.complete_enhanced_system = CompleteEnhancedFactorSystem(context)
                success = context.complete_enhanced_system.initialize_complete_system()
                if success:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 完整增强因子系统初始化成功 (192个因子)")
                    context.enhanced_factors_integration = None  # 清空旧系统
                else:
                    raise Exception("完整系统初始化失败")
            except:
                # 备用方案：使用原有系统
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔄 使用备用增强因子系统")
                context.enhanced_factors_integration = BacktestEnhancedIntegration(context)
                success = context.enhanced_factors_integration.initialize_enhanced_system()
                context.complete_enhanced_system = None
                if success:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 备用增强因子系统初始化成功 (52个因子)")
                else:
                    context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 备用增强因子系统初始化失败")
                    context.enhanced_factors_integration = None

        except Exception as e:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 增强因子系统初始化异常: {e}")
            context.enhanced_factors_integration = None
            context.complete_enhanced_system = None
    else:
        context.enhanced_factors_integration = None
        context.complete_enhanced_system = None
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ 增强因子系统不可用，跳过初始化")

def setup_buy_check_schedules(context):
    """设置买入检查的时间表"""
    try:
        # 检查是否启用了间隔模式
        interval_mode = get_config_value('BUY_CHECK_INTERVAL_MODE', False)
        
        # 添加调试日志，显示配置文件中的值和实际使用的值
        config_value = "未知"
        try:
            if hasattr(static_config, 'BUY_CHECK_INTERVAL_MODE'):
                config_value = getattr(static_config, 'BUY_CHECK_INTERVAL_MODE')
            elif _USE_DYNAMIC_CONFIG:
                config_value = get_param('BUY_CHECK_INTERVAL_MODE', "未设置")
        except:
            pass
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入检查模式配置值: {config_value}, 实际使用值: {interval_mode}, 使用动态配置: {_USE_DYNAMIC_CONFIG}")
        
        # 获取周期性买入检查设置
        enable_weekly_check = get_config_value('ENABLE_WEEKLY_LAST_DAY_CHECK', True)
        enable_monthly_check = get_config_value('ENABLE_MONTHLY_LAST_DAY_CHECK', True)
        
        # 设置周期性买入检查的时间点
        periodic_check_times = get_config_value('PERIODIC_CHECK_TIMES', ['14:30:00'])
        
        # 设置周期性买入检查
        if enable_weekly_check:
            # 每周最后一个交易日的买入检查
            schedule(
                schedule_func=lambda ctx: weekly_last_day_check(ctx),
                date_rule='1d',
                time_rule='09:35:00'  # 每天早盘开始时检查是否为每周最后交易日
            )
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置每周最后交易日买入检查")
        
        if enable_monthly_check:
            # 每月最后一个交易日的买入检查
            schedule(
                schedule_func=lambda ctx: monthly_last_day_check(ctx),
                date_rule='1d',
                time_rule='09:40:00'  # 每天早盘开始时检查是否为每月最后交易日
            )
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置每月最后交易日买入检查")
        
        if interval_mode:
            # 使用间隔模式设置买入检查
            interval_minutes = get_config_value('BUY_CHECK_INTERVAL_MINUTES', 30)
            
            # 验证间隔是否合理
            if interval_minutes < 1:
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入检查间隔设置过小({interval_minutes}分钟)，已调整为10分钟")
                interval_minutes = 10
            
            # 生成上午交易时段的时间点 (9:30-11:30)
            morning_start = datetime.time(9, 30)
            morning_end = datetime.time(11, 30)
            
            # 生成下午交易时段的时间点 (13:00-15:00)
            afternoon_start = datetime.time(13, 0)
            afternoon_end = datetime.time(15, 0)
            
            # 生成所有时间点
            check_times = []
            
            # 上午时段
            current_time = morning_start
            while current_time < morning_end:
                hour, minute = current_time.hour, current_time.minute
                time_str = f"{hour:02d}:{minute:02d}:00"
                check_times.append(time_str)
                
                # 增加分钟
                minute += interval_minutes
                # 处理进位
                while minute >= 60:
                    hour += 1
                    minute -= 60
                current_time = datetime.time(hour, minute)
            
            # 下午时段
            current_time = afternoon_start
            while current_time < afternoon_end:
                hour, minute = current_time.hour, current_time.minute
                time_str = f"{hour:02d}:{minute:02d}:00"
                check_times.append(time_str)
                
                # 增加分钟
                minute += interval_minutes
                # 处理进位
                while minute >= 60:
                    hour += 1
                    minute -= 60
                current_time = datetime.time(hour, minute)
            
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用间隔模式设置买入检查，间隔: {interval_minutes}分钟")
            
            # 为每个时间点设置调度
            for time_str in check_times:
                schedule(
                    schedule_func=buy_strategy,
                    date_rule='1d',
                    time_rule=time_str
                )
            
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置{len(check_times)}个买入检查时间点，间隔{interval_minutes}分钟")
            # 显示前5个和后5个时间点
            if len(check_times) > 10:
                first_times = ", ".join(check_times[:5])
                last_times = ", ".join(check_times[-5:])
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 前5个时间点: {first_times}...")
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 后5个时间点: ...{last_times}")
            else:
                all_times = ", ".join(check_times)
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 所有时间点: {all_times}")
            
        else:
            # 使用固定时间点模式
            # 从配置中获取买入检查时间点
            buy_check_times = get_config_value('BUY_CHECK_TIMES', [
                '09:31:00',  # 默认时间点，如果配置中没有定义
                '10:00:00',
                '10:30:00',
                '11:00:00',
                '13:30:00',
                '14:00:00',
                '14:30:00'
            ])
            
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用固定时间点模式设置买入检查")
            
            # 为每个时间点设置调度
            for time_str in buy_check_times:
                schedule(
                    schedule_func=buy_strategy,
                    date_rule='1d',
                    time_rule=time_str
                )
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置买入检查时间点: {time_str}")
            
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 共设置了 {len(buy_check_times)} 个买入检查时间点")
    
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置买入检查时间表异常: {str(e)}")
        # 如果设置失败，使用默认时间点
        schedule(schedule_func=buy_strategy, date_rule='1d', time_rule='09:31:00')
        schedule(schedule_func=buy_strategy, date_rule='1d', time_rule='10:30:00')
        schedule(schedule_func=buy_strategy, date_rule='1d', time_rule='14:00:00')
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置买入检查时间表失败，使用默认时间点")

def weekly_last_day_check(context):
    """每周最后一个交易日的买入检查"""
    try:
        # 检查今天是否是本周最后一个交易日
        if is_last_trading_day_of_period(context, 'week'):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 今天是本周最后一个交易日，执行周线买入检查")
            
            # 获取周期性买入检查的时间点
            periodic_check_times = get_config_value('PERIODIC_CHECK_TIMES', ['14:30:00'])
            
            # 为每个时间点设置当天的一次性调度
            for time_str in periodic_check_times:
                # 解析时间字符串
                hour, minute, second = map(int, time_str.split(':'))
                check_time = datetime.time(hour, minute, second)
                
                # 检查当前时间是否已经超过了检查时间
                current_time = context.now.time()
                if current_time < check_time:
                    # 如果还没到检查时间，设置一次性调度
                    schedule(
                        schedule_func=lambda ctx: weekly_buy_strategy(ctx),
                        date_rule='1d',
                        time_rule=time_str
                    )
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置本周最后交易日 {time_str} 的买入检查")
                else:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前时间 {current_time} 已超过检查时间 {check_time}，跳过设置")
        else:
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 今天不是本周最后一个交易日，跳过周线买入检查")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 每周最后交易日检查异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

def monthly_last_day_check(context):
    """每月最后一个交易日的买入检查"""
    try:
        # 检查今天是否是本月最后一个交易日
        if is_last_trading_day_of_period(context, 'month'):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 今天是本月最后一个交易日，执行月线买入检查")
            
            # 获取周期性买入检查的时间点
            periodic_check_times = get_config_value('PERIODIC_CHECK_TIMES', ['14:30:00'])
            
            # 为每个时间点设置当天的一次性调度
            for time_str in periodic_check_times:
                # 解析时间字符串
                hour, minute, second = map(int, time_str.split(':'))
                check_time = datetime.time(hour, minute, second)
                
                # 检查当前时间是否已经超过了检查时间
                current_time = context.now.time()
                if current_time < check_time:
                    # 如果还没到检查时间，设置一次性调度
                    schedule(
                        schedule_func=lambda ctx: monthly_buy_strategy(ctx),
                        date_rule='1d',
                        time_rule=time_str
                    )
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置本月最后交易日 {time_str} 的买入检查")
                else:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前时间 {current_time} 已超过检查时间 {check_time}，跳过设置")
        else:
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 今天不是本月最后一个交易日，跳过月线买入检查")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 每月最后交易日检查异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

def weekly_buy_strategy(context):
    """周线买入策略，在每周最后一个交易日执行"""
    try:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 执行周线买入策略")
        
        # 执行买入策略
        buy_strategy(context)
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 周线买入策略执行完成")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 周线买入策略异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

def monthly_buy_strategy(context):
    """月线买入策略，在每月最后一个交易日执行"""
    try:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 执行月线买入策略")
        
        # 执行买入策略
        buy_strategy(context)
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 月线买入策略执行完成")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 月线买入策略异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

def setup_position_summary_schedules(context):
    """设置持仓摘要输出的时间表"""
    try:
        # 检查是否启用持仓摘要
        enable_position_summary = get_config_value('ENABLE_POSITION_SUMMARY', True)
        if not enable_position_summary:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓摘要功能已禁用，跳过设置持仓摘要输出时间表")
            return
            
        # 从配置中获取持仓摘要输出时间点
        position_summary_times = get_config_value('POSITION_SUMMARY_TIMES', [
            '10:30:00',  # 默认时间点，如果配置中没有定义
            '14:30:00'
        ])
        
        # 为每个时间点设置调度
        for time_str in position_summary_times:
            schedule(
                schedule_func=position_summary,
                date_rule='1d',
                time_rule=time_str
            )
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已设置持仓摘要输出时间点: {time_str}")
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 共设置了 {len(position_summary_times)} 个持仓摘要输出时间点")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置持仓摘要输出时间表异常: {str(e)}")
        # 如果设置失败，使用默认时间点
        schedule(schedule_func=position_summary, date_rule='1d', time_rule='10:30:00')
        schedule(schedule_func=position_summary, date_rule='1d', time_rule='14:30:00')
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置持仓摘要输出时间表失败，使用默认时间点")

def init(context):
    # 获取是否使用动态配置
    global _USE_DYNAMIC_CONFIG
    global os  # 添加这一行，声明os为全局变量
    _USE_DYNAMIC_CONFIG = os.environ.get('USE_DYNAMIC_CONFIG', '0') == '1'
    
    # 初始化动态配置标志
    context.use_dynamic_config = _USE_DYNAMIC_CONFIG
    
    # 先初始化日志系统，确保context.log可用
    context.log = setup_logger('strategy_log', 'strategy.log')
    
    # 添加7*24小时运行模式相关变量
    context.subscribed_today = False  # 当天是否已执行过订阅
    context.last_subscription_date = None  # 上次订阅的日期
    context.last_date = context.now.date()  # 初始化last_date为当前日期
    context.subscription_errors = 0  # 订阅错误计数
    context.subscription_error_cooldown = False  # 订阅错误冷却标志
    context.last_subscription_error_time = None  # 上次订阅错误时间

    # 添加当日买入记录跟踪机制
    context.today_bought_symbols = set()  # 当日已买入的股票代码集合
    context.last_buy_date = None  # 上次买入的日期，用于每日重置

    # 🚀 初始化性能优化组件
    try:
        context.global_data_cache = GlobalDataCacheManager(context)
        context.timestamp_cache = TimestampCacheManager(context)
        context.trix_cache_manager = TrixCacheManager(context)
        context.batch_db_manager = BatchDatabaseManager(context)
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 性能优化组件初始化成功")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 性能优化组件初始化失败: {e}")
        # 设置默认值以确保兼容性
        context.global_data_cache = None
        context.timestamp_cache = None
        context.trix_cache_manager = None
        context.batch_db_manager = None
    
    # 初始化最大买入批次参数
    context.max_buy_batch = get_config_value('MAX_BUY_BATCH', 15)  # 每次最多买入的股票数量
    
    # 设置运行模式
    try:
        # 尝试获取运行模式
        if 'get_run_mode' in globals():
            mode = get_run_mode()
            if mode == 0:
                context.run_mode = 'backtest'
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 通过API获取运行模式: 回测模式")
            else:
                context.run_mode = 'live'
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 通过API获取运行模式: 实盘/模拟盘模式")
        else:
            # 默认设置为回测模式
            context.run_mode = 'backtest'
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取运行模式，默认设置为回测模式")
    except Exception as e:
        context.run_mode = 'backtest'
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取运行模式异常: {str(e)}，默认设置为回测模式")
    
    # 将全局配置获取函数添加到context中，供其他模块使用
    context.get_config_value = get_config_value
    
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 策略初始化开始")
    context.log.info(f"使用{'动态' if context.use_dynamic_config else '静态'}配置模式")
    context.log.info(f"运行模式: {context.run_mode}")
    
    # 初始化性能分析器
    enable_profiling = get_config_value('ENABLE_PERFORMANCE_PROFILING', False)
    profiling_threshold = get_config_value('PROFILING_THRESHOLD_MS', 10)
    profiling_output_file = get_config_value('PROFILING_OUTPUT_FILE', None)
    profiling_top_functions = get_config_value('PROFILING_TOP_FUNCTIONS', 10)
    profiling_summary_interval = get_config_value('PROFILING_SUMMARY_INTERVAL', 300)

    # 🚀 初始化高级性能优化器
    try:
        from advanced_performance_optimizer import AdvancedPerformanceOptimizer
        context.advanced_optimizer = AdvancedPerformanceOptimizer(context)
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 高级性能优化器初始化完成")
    except Exception as e:
        context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 高级性能优化器初始化失败: {e}")
        context.advanced_optimizer = None

    # 🎯 一次性检查智能评分系统状态（避免每次都检查）
    context.smart_scoring_enabled_global = getattr(context, 'smart_scoring_enabled', False) and hasattr(context, 'scoring_system')
    if context.smart_scoring_enabled_global:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 智能评分系统已启用，将用于买入决策")
    else:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 智能评分系统未启用，将使用基础TRIX信号")
    
    # 初始化性能分析器
    context.profiler = init_profiler(
        context=context,
        enabled=enable_profiling,
        threshold_ms=profiling_threshold,
        output_file=profiling_output_file,
        top_functions=profiling_top_functions,
        summary_interval=profiling_summary_interval
    )
    
    if enable_profiling:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 性能分析已启用，阈值: {profiling_threshold}ms, 汇总间隔: {profiling_summary_interval}秒")
    
    # 初始化数据库（确保包含完整的增强指标字段）
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始初始化数据库结构...")
    try:
        db_initializer = DatabaseInitializer()

        # 检查数据库是否需要初始化
        if not db_initializer.verify_database_schema():
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 数据库结构不完整，正在重新初始化...")
            success = db_initializer.initialize_all_databases()
            if success:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 数据库初始化成功，支持完整的增强指标")
            else:
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 数据库初始化失败")
        else:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 数据库结构完整，无需重新初始化")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 数据库初始化异常: {e}")
        # 备用：使用原有初始化方式
        init_csv_files()
        try:
            data_manager._check_and_update_table_structure(sqlite3.connect("data/trades.db"))
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 备用数据库表结构检查完成")
        except Exception as e2:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 备用数据库表结构检查失败: {str(e2)}")

    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 数据库初始化完成")
    
    # 设置全局变量
    context.positions = {}  # 持仓信息 {symbol: volume}
    context.positions_cost = {}  # 持仓成本信息 {symbol: {'cost_price': price, 'buy_time': datetime, 'confirmed_high': price, 'confirmed_time': datetime}}
    # 只有当反弹买入策略启用时才初始化confirmed_lows字典
    if get_config_value('ENABLE_REBOUND_BUY', False):
        context.confirmed_lows = {}  # 用于跟踪股票的确认低点 {symbol: {'price': price, 'time': datetime}}
    else:
        context.confirmed_lows = None  # 反弹买入策略未启用，不需要跟踪最低点
    context.last_check_time = None  # 上次检查时间
    context.last_position_check = None  # 上次持仓检查时间
    context.last_signal_check_time = None  # 上次信号检查时间
    context.buy_check_count = 0  # 买入检查次数
    
    # 初始化持仓管理器
    context.position_manager = PositionManager(context)
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓管理器初始化完成")

    # 初始化增强指标集成器
    initialize_enhanced_indicators(context)

    # 初始化性能优化器
    context.performance_optimizer = get_performance_optimizer(context)
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 性能优化器初始化完成")

    # 初始化TRIX预筛选器
    context.trix_prefilter = get_trix_prefilter(context)
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - TRIX预筛选器初始化完成")
    
    # 配置管理
    if context.use_dynamic_config:
        context.config_manager = config_manager
        context.config = config_manager.get_config()
        
        # 启动配置监控
        context.config_manager.start_monitoring()
        # 不再重新初始化日志
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置管理器初始化完成，将动态监控配置变化")
    else:
        # 静态配置模式
        context.config = static_config
        # 不再重新初始化日志
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用静态配置模式，配置变更需要重启策略才能生效")
    
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 策略日志系统初始化完成")
    
    # Initialize CSV files - 使用数据管理器
    init_csv_files()
    
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 数据管理系统初始化完成")
    
    # 初始化自定义订阅股票列表
    custom_subscribe_mode = get_config_value('CUSTOM_SUBSCRIBE_MODE', False)
    custom_subscribe_symbols = get_config_value('CUSTOM_SUBSCRIBE_SYMBOLS', [])
    
    if custom_subscribe_mode and custom_subscribe_symbols:
        context.custom_symbols = custom_subscribe_symbols
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已从配置文件加载自定义订阅股票列表: {context.custom_symbols}")
        # 在自定义订阅模式下，不使用参考指数
        context.index_symbol = None
        context.subscribe_all_market = False
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 自定义订阅模式下，不使用参考指数")
    else:
        context.custom_symbols = None
        # 从配置文件初始化参数
        context.index_symbol = get_config_value('INDEX_SYMBOL', 'SHSE.000300')
        context.subscribe_all_market = get_config_value('SUBSCRIBE_ALL_MARKET', False)
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 未启用自定义订阅模式，使用参考指数: {context.index_symbol}")
    
    # 初始化其他参数
    context.max_positions = get_config_value('MAX_POSITIONS', 95)
    context.ratio = get_config_value('POSITION_RATIO', 0.9)
    context.trailing_stop = get_config_value('TRAILING_STOP', 0.015)
    context.min_holding_days_for_trailing_stop = get_config_value('MIN_HOLDING_DAYS', 1)
    context.t_plus_1 = get_config_value('T_PLUS_1', True)
    context.risk_check_enabled = get_config_value('RISK_CHECK_ENABLED', False)
    context.last_position_check = None  # 上次持仓检查时
    context.buy_check_count = 0  # 买入检查次数计数
    
    # 初始化交易执行参数
    context.use_market_order = get_config_value('USE_MARKET_ORDER', True)  # 是否使用市价单
    context.price_adjust_ratio = get_config_value('PRICE_ADJUST_RATIO', 0.002)  # 价格调整比例
    
    # 初始化历史数据获取参数
    context.history_data_days = get_config_value('HISTORY_DATA_DAYS', 50)  # 一般技术指标历史数据天数
    context.history_data_days_volatility = get_config_value('HISTORY_DATA_DAYS_VOLATILITY', 60)  # 波动性计算历史数据天数
    
    # 初始化信号开关参数
    context.enable_buy_signals = get_config_value('ENABLE_BUY_SIGNALS', True)  # 买入信号总开关
    context.enable_trix_buy_signal = get_config_value('ENABLE_TRIX_BUY_SIGNAL', True)  # TRIX买入信号
    context.enable_ma_cross_buy_signal = get_config_value('ENABLE_MA_CROSS_BUY_SIGNAL', False)  # 均线交叉买入信号
    context.enable_sell_signals = get_config_value('ENABLE_SELL_SIGNALS', True)  # 卖出信号总开关
    context.enable_trailing_stop = get_config_value('ENABLE_TRAILING_STOP', True)  # 跟踪止盈
    context.enable_trix_sell_signal = get_config_value('ENABLE_TRIX_SELL_SIGNAL', True)  # TRIX死叉卖出
    context.enable_dynamic_stop_loss = get_config_value('ENABLE_DYNAMIC_STOP_LOSS', True)  # 动态止损
    
    # 初始化TRIX参数
    context.trix_ema_period = get_config_value('TRIX_EMA_PERIOD', 3)  # TRIX买入信号的EMA周期
    context.trix_sell_ema_period = get_config_value('TRIX_SELL_EMA_PERIOD', 6)  # TRIX卖出信号的EMA周期
    context.use_talib_trix = get_config_value('USE_TALIB_TRIX', False)  # 是否使用talib直接计算TRIX
    
    # 初始化止损参数
    context.dynamic_stop_loss_ratio = get_config_value('DYNAMIC_STOP_LOSS_RATIO', 0.05)  # 动态止损比例
    
    # 初始化信号检查和持仓更新参数
    context.signal_check_interval = get_config_value('SELL_SIGNAL_CHECK_INTERVAL', 10)  # 卖出信号计算间隔(分钟)
    context.position_check_interval = get_config_value('POSITION_CHECK_INTERVAL', 30)  # 持仓更新间隔(分钟)
    
    # 初始化波动性参数
    context.market_volatility = get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)  # 初始市场波动率
    context.volatility_cache = {}  # 波动性缓存
    context.max_volatility_factor = get_config_value('MAX_VOLATILITY_FACTOR', 3.0)  # 最大波动性资金调整因子
    context.min_volatility_factor = get_config_value('MIN_VOLATILITY_FACTOR', 1.0)  # 最小波动性资金调整因子
    context.volatility_factor_scale = get_config_value('VOLATILITY_FACTOR_SCALE', 1.5)  # 波动性因子缩放系数
    
    # 初始化仓位控制参数
    context.single_position_limit = get_config_value('SINGLE_POSITION_LIMIT', 150000)  # 单只股票最大持仓金额
    context.single_position_ratio = get_config_value('SINGLE_POSITION_RATIO', 0.08)  # 单只股票最大持仓比例
    context.dynamic_position_max_ratio = get_config_value('DYNAMIC_POSITION_MAX_RATIO', 0.15)  # 动态调整后单只股票最大持仓比例
    context.min_position_limit = get_config_value('MIN_POSITION_LIMIT', 1000)  # 单只股票最小持仓金额
    context.min_position_ratio = get_config_value('MIN_POSITION_RATIO', 0.03)  # 单只股票最小持仓比例
    context.max_position_pct = context.single_position_ratio  # 单只股票最大持仓比例（用于买入策略）
    context.min_position_pct = context.min_position_ratio  # 单只股票最小持仓比例（用于买入策略）
    
    # 输出配置文件中的持仓比例值
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置文件中的单只股票最大持仓比例: {get_config_value('SINGLE_POSITION_RATIO', 0.15)*100:.2f}%, 实际使用值: {context.single_position_ratio*100:.2f}%")
    
    # 初始化波动性因子参数
    context.max_volatility_factor = get_config_value('MAX_VOLATILITY_FACTOR', 3.0)
    context.min_volatility_factor = get_config_value('MIN_VOLATILITY_FACTOR', 1.0)
    context.volatility_factor_scale = get_config_value('VOLATILITY_FACTOR_SCALE', 1.5)
    
    # 日志系统设置
    # 从环境变量中读取分析日志开关设置
    enable_analysis_log = os.environ.get('ENABLE_ANALYSIS_LOG', '1')
    context.enable_analysis_log = (enable_analysis_log == '1')
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 分析日志输出: {'启用' if context.enable_analysis_log else '禁用'}")
    
    # CSV日志设置
    context.enable_csv_logging = get_config_value('ENABLE_CSV_LOGGING', False)
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV日志写入: {'启用' if context.enable_csv_logging else '禁用'}")
    
    # 波动性相关参数
    context.volatility_period = get_config_value('VOLATILITY_PERIOD', 20)
    context.volatility_threshold = get_config_value('VOLATILITY_THRESHOLD', 1.0)
    context.atr_threshold = get_config_value('ATR_THRESHOLD', 1.5)
    context.market_volatility = get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
    context.volatility_cache = {}  # 缓存股票波动率计算结果，避免重复计算
    context.max_volatility_factor = get_config_value('MAX_VOLATILITY_FACTOR', 3.0)  # 最大波动性资金调整因子
    context.min_volatility_factor = get_config_value('MIN_VOLATILITY_FACTOR', 1.0)  # 最小波动性资金调整因子
    context.volatility_factor_scale = get_config_value('VOLATILITY_FACTOR_SCALE', 1.5)  # 波动性因子缩放系数
    

    
    # 初始化波动性因子参数
    context.max_volatility_factor = get_config_value('MAX_VOLATILITY_FACTOR', 3.0)
    context.min_volatility_factor = get_config_value('MIN_VOLATILITY_FACTOR', 1.0)
    context.volatility_factor_scale = get_config_value('VOLATILITY_FACTOR_SCALE', 1.5)
    
    # 日志系统设置
    # 从环境变量中读取分析日志开关设置
    enable_analysis_log = os.environ.get('ENABLE_ANALYSIS_LOG', '1')
    context.enable_analysis_log = (enable_analysis_log == '1')
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 分析日志输出: {'启用' if context.enable_analysis_log else '禁用'}")
    
    # CSV日志设置
    context.enable_csv_logging = get_config_value('ENABLE_CSV_LOGGING', False)
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV日志写入: {'启用' if context.enable_csv_logging else '禁用'}")
    
    # 波动性相关参数
    context.volatility_period = get_config_value('VOLATILITY_PERIOD', 20)
    context.volatility_threshold = get_config_value('VOLATILITY_THRESHOLD', 1.0)
    context.atr_threshold = get_config_value('ATR_THRESHOLD', 1.5)
    context.market_volatility = get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
    context.volatility_cache = {}  # 缓存股票波动率计算结果，避免重复计算
    context.volatility_cache_expire = get_config_value('CACHE_EXPIRE_SECONDS', 3600)
    context.last_market_volatility_update = None  # 上次更新市场波动率的时间

    # Store fieldnames in context for easy access
    context.ANALYSIS_FIELDNAMES = ANALYSIS_FIELDNAMES
    context.TRADE_FIELDNAMES = TRADE_FIELDNAMES

    # 初始化模型
    context.data_fetcher = DataFetcher(context)
    context.signal_generator = SignalGenerator(context)
    
    # 初始化历史数据管理器 - 优化数据获取性能
    context.history_data_manager = HistoryDataManager(context, cache_size=get_config_value('HISTORY_CACHE_SIZE', 1500))
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 历史数据管理器初始化完成，缓存大小: {get_config_value('HISTORY_CACHE_SIZE', 500)}")
    
    # 如果启用了历史数据预获取，则在初始化时预获取指数成分股的历史数据
    if get_config_value('ENABLE_HISTORY_PREFETCH', True):
        # 定义一个预获取函数，在策略初始化完成后执行
        def prefetch_history_data(ctx):
            """预加载历史数据"""
            try:
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始预加载历史数据")
                
                # 获取历史数据天数，默认50天
                history_days = get_config_value('HISTORY_DATA_DAYS', 50)
                
                # 获取指数成分股
                ctx.data_fetcher.fetch_index_constituents(ctx.index_symbol)
                symbols = ctx.data_fetcher.constituents
                
                if not symbols:
                    ctx.log.warning(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 无有效成分股，跳过历史数据预加载")
                    return
                    
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 预加载{len(symbols)}只股票的历史数据")
                
                # 批量获取历史数据
                start_time = ctx.now - datetime.timedelta(days=history_days)
                hist_data = history(symbols=symbols, frequency='1d', 
                                    start_time=start_time, end_time=ctx.now,
                                    fields='open,high,low,close,volume',
                                    df=True)
                                    
                if hist_data is None or hist_data.empty:
                    ctx.log.warning(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 未能获取历史数据")
                    return
                    
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功获取历史数据: {len(hist_data)}条记录")
                
                # 预先计算并缓存每只股票的最低价信息到confirmed_lows
                symbols_processed = 0
                # 只有当反弹买入策略启用时才初始化最低点
                if get_config_value('ENABLE_REBOUND_BUY', False) and hasattr(ctx, 'confirmed_lows') and ctx.confirmed_lows is not None:
                    for symbol in symbols:
                        try:
                            # 获取该股票的历史数据
                            symbol_data = hist_data[hist_data['symbol'] == symbol]
                            if symbol_data.empty:
                                continue
                                
                            # 获取最近监控期内的数据
                            rebound_period = get_config_value('REBOUND_PERIOD', 5)
                            recent_data = symbol_data.tail(rebound_period)
                            
                            if not recent_data.empty:
                                # 找出最低价及其对应的日期
                                min_idx = recent_data['low'].idxmin()
                                min_price = recent_data.loc[min_idx, 'low']
                                min_time = recent_data.loc[min_idx, 'eob']
                                
                                # 计算与当前日期的天数差
                                days_since_low = (ctx.now.replace(tzinfo=None) - min_time.replace(tzinfo=None)).days
                                
                                # 初始化确认低点
                                ctx.confirmed_lows[symbol] = {
                                    'price': min_price,
                                    'time': min_time
                                }
                                
                                symbols_processed += 1
                        except Exception as e:
                            ctx.log.error(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 预处理{symbol}历史低点异常: {str(e)}")
                
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 已初始化{symbols_processed}只股票的确认低点")
            except Exception as e:
                ctx.log.error(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 预加载历史数据异常: {str(e)}")
    
    context.trade_executor = TradeExecutor(context) 
    context.risk_manager = RiskManager(context)
    
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 策略参数初始化完成，遵循A股T+1交易规则")
    
    # 输出订阅信息
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 将订阅{context.index_symbol}成分股行情")

    # 初始化订阅参数
    context.subscribe_mode = get_config_value('SUBSCRIBE_MODE', 'auto')
    context.batch_size = get_config_value('BATCH_SIZE', 50)
    context.subscribe_rotation_seconds = get_config_value('SUBSCRIBE_ROTATION_SECONDS', 0)
    context.subscribe_priority_holding = get_config_value('SUBSCRIBE_PRIORITY_HOLDING', True)
    context.execute_buy_after_subscribe = get_config_value('EXECUTE_BUY_AFTER_SUBSCRIBE', True)
    
    # 初始化订阅轮换参数
    mode_adaptive_setting = get_config_value('ENABLE_MODE_ADAPTIVE', 0)
    
    # 处理模式自适应设置
    if mode_adaptive_setting == 0:
        # 自动模式自适应，根据run_mode自动判断
        context.enable_mode_adaptive = True
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用自动模式自适应")
    elif mode_adaptive_setting == 1:
        # 强制使用回测模式订阅策略
        context.enable_mode_adaptive = False
        context.run_mode = 'backtest'
        context.backtest_full_subscription = True
        # 初始化订阅状态为未订阅，确保首次订阅能够执行
        context.subscribed_today = False
        context.last_subscription_date = None
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用回测模式订阅策略")
    elif mode_adaptive_setting == 2:
        # 强制使用模拟盘模式订阅策略
        context.enable_mode_adaptive = False
        context.run_mode = 'live'
        context.subscription_rotation_enabled = True
        context.live_mode_batch_size = 100  # 模拟盘使用较大批次
        context.subscription_rotation_interval = 2  # 模拟盘使用较长轮换间隔
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用模拟盘模式订阅策略")
    elif mode_adaptive_setting == 3:
        # 强制使用实盘模式订阅策略
        context.enable_mode_adaptive = False
        context.run_mode = 'live'
        context.subscription_rotation_enabled = True
        context.live_mode_batch_size = 50   # 实盘使用较小批次
        context.subscription_rotation_interval = 1  # 实盘使用较短轮换间隔
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用实盘模式订阅策略")
    else:
        # 未知设置，使用默认值
        context.enable_mode_adaptive = True
        context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 未知的模式自适应设置: {mode_adaptive_setting}，使用默认自动模式")
    
    context.subscription_rotation_enabled = get_config_value('SUBSCRIPTION_ROTATION_ENABLED', True)
    context.subscription_rotation_interval = get_config_value('SUBSCRIPTION_ROTATION_INTERVAL', 1)
    context.live_mode_batch_size = get_config_value('LIVE_MODE_BATCH_SIZE', 50)
    context.max_subscription_limit = get_config_value('MAX_SUBSCRIPTION_LIMIT', 50)
    context.max_subscription_per_batch = get_config_value('MAX_SUBSCRIPTION_PER_BATCH', 50)
    context.sync_buy_check_with_rotation = get_config_value('SYNC_BUY_CHECK_WITH_ROTATION', True)
    context.backtest_full_subscription = get_config_value('BACKTEST_FULL_SUBSCRIPTION', True)
    context.backtest_subscribe_once = get_config_value('BACKTEST_SUBSCRIBE_ONCE', True)
    
    # 在实盘/模拟盘模式下，确保使用批次订阅
    if context.run_mode == 'live':
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘模式，强制使用批次订阅")
        context.subscribe_mode = 'batch'
        context.subscription_rotation_enabled = True
        # 确保轮换间隔合理
        if context.subscription_rotation_interval <= 0:
            context.subscription_rotation_interval = 1
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换间隔设置为 {context.subscription_rotation_interval} 分钟")
        
        # 确保批次大小不超过最大订阅限制
        context.live_mode_batch_size = min(context.live_mode_batch_size, context.max_subscription_limit)
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 批次大小设置为 {context.live_mode_batch_size}")
    
    # 输出订阅管理配置
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅管理配置:")
    context.log.info(f"订阅模式: {context.subscribe_mode}")
    context.log.info(f"批次大小: {context.batch_size}")
    context.log.info(f"轮换间隔: {context.subscribe_rotation_seconds}秒")
    context.log.info(f"优先订阅持仓: {'是' if context.subscribe_priority_holding else '否'}")
    context.log.info(f"订阅后执行买入: {'是' if context.execute_buy_after_subscribe else '否'}")
    context.log.info(f"订阅轮换: {'启用' if context.subscription_rotation_enabled else '禁用'}")
    
    # 定义一个具名函数来替代lambda表达式
    def subscribe_constituents_func(ctx):
        """
        订阅成分股函数，只在交易时间段内执行订阅
        
        修改说明:
        1. 在实盘/模拟盘模式下直接使用批次订阅
        2. 避免不必要的全量订阅尝试
        3. 在强制回测模式下避免重复订阅
        4. 支持BACKTEST_SUBSCRIBE_ONCE参数，回测模式下只订阅一次
        5. 处理index_symbol为None的情况
        """
        # 检查是否在交易时间段或回测模式
        if is_trading_hour(ctx.now) or ctx.run_mode == 'backtest':
            # 检查回测模式下是否启用了只订阅一次的功能
            if ctx.run_mode == 'backtest':
                backtest_subscribe_once = get_config_value('BACKTEST_SUBSCRIBE_ONCE', True)
                if backtest_subscribe_once and hasattr(ctx, 'subscribed_today') and ctx.subscribed_today:
                    ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式已启用BACKTEST_SUBSCRIBE_ONCE，跳过重复订阅")
                    return
            
            # 检查是否是强制回测模式且已经订阅过
            mode_adaptive_setting = get_config_value('ENABLE_MODE_ADAPTIVE', 0)
            if mode_adaptive_setting == 1 and hasattr(ctx, 'subscribed_today') and ctx.subscribed_today:
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下已经订阅过，跳过重复订阅")
                return
            
            # 检查是否启用了自定义订阅模式
            custom_subscribe_mode = get_config_value('CUSTOM_SUBSCRIBE_MODE', False)
            if custom_subscribe_mode:
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 已启用自定义订阅模式，跳过指数成分股订阅")
                return
                
            # 检查index_symbol是否为None
            if ctx.index_symbol is None:
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 参考指数为None，跳过成分股订阅")
                return
                
            # 根据配置决定是订阅全市场还是指数成分股
            if get_config_value('SUBSCRIBE_ALL_MARKET', False):
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅全市场股票")
                ctx.data_fetcher.subscribe_constituents('ALL')
            else:
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅指数 {ctx.index_symbol} 成分股")
                ctx.data_fetcher.subscribe_constituents(ctx.index_symbol)
            
            # 更新订阅状态
            ctx.subscribed_today = True
            ctx.last_subscription_date = ctx.now.date()
            ctx.subscription_errors = 0
            ctx.subscription_error_cooldown = False
        else:
            ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过订阅操作")
    
    # 初始订阅成分股
    schedule(
        schedule_func=subscribe_constituents_func,
        date_rule='1d',
        time_rule='09:30:00'
    )
    
    # 如果启用了自动轮换，添加定期轮换订阅的调度
    if context.subscribe_rotation_seconds > 0:
        def rotate_subscription_func(ctx):
            """
            轮换订阅函数，只在交易时间段内执行轮换
            
            修改说明:
            1. 确保在轮换订阅时优先订阅持仓股票
            2. 在实盘/模拟盘模式下使用优化的批次订阅
            3. 支持BACKTEST_SUBSCRIBE_ONCE参数，回测模式下只订阅一次
            4. 处理自定义订阅模式
            """
            # 检查是否在交易时间段或回测模式
            if is_trading_hour(ctx.now) or ctx.run_mode == 'backtest':
                # 检查回测模式下是否启用了只订阅一次的功能
                if ctx.run_mode == 'backtest':
                    backtest_subscribe_once = get_config_value('BACKTEST_SUBSCRIBE_ONCE', True)
                    if backtest_subscribe_once and hasattr(ctx, 'subscribed_today') and ctx.subscribed_today:
                        ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式已启用BACKTEST_SUBSCRIBE_ONCE，跳过轮换订阅")
                        return
                
                # 检查是否启用了自定义订阅模式
                custom_subscribe_mode = get_config_value('CUSTOM_SUBSCRIBE_MODE', False)
                if custom_subscribe_mode:
                    ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 已启用自定义订阅模式，跳过轮换订阅")
                    return
                
                # 确保持仓股票被优先订阅
                if hasattr(ctx, 'positions') and ctx.positions:
                    ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅前确保持仓股票优先")
                    ctx.data_fetcher.current_subscribed_batch = list(ctx.positions.keys())
                
                # 执行轮换订阅
                ctx.data_fetcher.rotate_subscription(ctx.index_symbol)
                
                # 同步买入检查
                if hasattr(ctx, 'sync_buy_check_with_rotation') and ctx.sync_buy_check_with_rotation:
                    if hasattr(ctx, 'buy_check_interval_mode') and ctx.buy_check_interval_mode:
                        ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅后同步执行买入检查")
                        buy_strategy(ctx)
            else:
                ctx.log.info(f"{ctx.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过轮换订阅操作")
    
    # 定期刷新订阅列表
    schedule(
        schedule_func=refresh_subscriptions,
        date_rule='1d',
        time_rule='09:05:00' # 在市场开盘后5分钟
    )
    
    # 定期预获取历史数据
    if get_config_value('ENABLE_HISTORY_PREFETCH', True):
        schedule(
            schedule_func=prefetch_history_data,
            date_rule='1d',
            time_rule='09:25:00'  # 在开盘前预获取数据
        )
    
    # 设置持仓摘要输出时间表 - 使用配置文件中的时间点
    setup_position_summary_schedules(context)
    
    # 定期更新市场波动率
    schedule(
        schedule_func=update_market_volatility,
        date_rule='1d',
        time_rule='09:35:00'  # 在市场开盘后更新
    )
    
    # 定期重新加载配置 - 只在启用动态配置时添加
    if context.use_dynamic_config:
        # 定义一个具名函数来替代直接传递reload_config
        def reload_config_wrapper(ctx):
            reload_config(ctx)
            
        schedule(
            schedule_func=reload_config_wrapper,
            date_rule='1d',
            time_rule='09:00:00'  # 每日交易开始前重新加载配置
        )
        # 中午也重新加载一次
        schedule(
            schedule_func=reload_config_wrapper,
            date_rule='1d',
            time_rule='13:00:00'  # 午间休市时重新加载配置
        )
    
    # 设置每日预筛选任务（开盘时执行）
    schedule(
        schedule_func=daily_prefilter_task,
        date_rule='1d',
        time_rule='09:30:00'  # 开盘时立即执行预筛选
    )

    # 设置买入检查时间表 - 使用配置文件中的时间点
    setup_buy_check_schedules(context)
    
    # 输出策略启动摘要
    context.log.info(f"""
    ========== 策略启动摘要 ==========
    时间: {context.now.strftime('%Y-%m-%d %H:%M:%S')}
    指数: {context.index_symbol}
    全市场订阅: {'启用' if context.subscribe_all_market else '禁用'}
    最大持仓数: {context.max_positions}
    最大订阅数: {context.data_fetcher.max_subscribe_count}
    T+1交易规则: {'启用' if context.t_plus_1 else '禁用'}
    跟踪止盈阈值 {context.trailing_stop*100:.1f}%
    最小持仓天数 {context.min_holding_days_for_trailing_stop}天
    
    交易执行设置:
    订单类型: {'市价单' if context.use_market_order else '限价单'}
    价格调整比例: {context.price_adjust_ratio*100:.2f}%
    
    历史数据获取设置:
    一般技术指标历史数据天数: {context.history_data_days}天
    波动性计算历史数据天数: {context.history_data_days_volatility}天
    历史数据缓存大小: {getattr(context.history_data_manager, 'cache_size', 0)}条
    历史数据预获取: {'启用' if get_config_value('ENABLE_HISTORY_PREFETCH', True) else '禁用'}
    
    信号开关设置:
    买入信号总开关: {'启用' if context.enable_buy_signals else '禁用'}
    TRIX买入信号: {'启用' if context.enable_trix_buy_signal else '禁用'}
    均线交叉买入信号: {'启用' if context.enable_ma_cross_buy_signal else '禁用'}
    卖出信号总开关: {'启用' if context.enable_sell_signals else '禁用'}
    跟踪止盈: {'启用' if context.enable_trailing_stop else '禁用'}
    TRIX死叉卖出: {'启用' if context.enable_trix_sell_signal else '禁用'}
    动态止损: {'启用' if context.enable_dynamic_stop_loss else '禁用'}
    
    TRIX指标设置:
    TRIX计算周期: {context.trix_ema_period}
    TRIX计算方式: {'使用talib.TRIX' if context.use_talib_trix else '使用自定义计算'}
    
    波动性入场: 启用（优先买入高波动性股票）
    波动性周期 {context.volatility_period}天
    波动性阈值 市场波动率的{context.volatility_threshold}倍或ATR>{context.atr_threshold}%或波动率>1.8%
    波动性评分 波动率权重70%，ATR权重30%
    
    仓位管理:
    单只股票最大持仓金额: {context.single_position_limit}元
    单只股票最大持仓比例: {context.single_position_ratio*100:.2f}%
    单只股票最小持仓金额: {context.min_position_limit}元
    单只股票最小持仓比例: {context.min_position_ratio*100:.2f}%
    动态仓位最大比例: {context.dynamic_position_max_ratio*100:.1f}%
    波动性资金调整因子范围: {context.min_volatility_factor} - {context.max_volatility_factor}
    
    订阅管理:
    模式自适应: {'启用' if context.enable_mode_adaptive else '禁用'}
    订阅轮换: {'启用' if context.subscription_rotation_enabled else '禁用'}
    轮换间隔: {context.subscription_rotation_interval}分钟
    批次大小: {context.live_mode_batch_size}
    最大订阅限制: {context.max_subscription_limit}
    运行模式: {context.run_mode}
    订阅模式: {'batch' if context.subscription_rotation_enabled else 'full'}
    回测全量订阅: {'启用' if context.backtest_full_subscription else '禁用'}
    回测只订阅一次: {'启用' if context.backtest_subscribe_once else '禁用'}
    
    性能优化:
    性能优化总开关: {'启用' if get_config_value('ENABLE_PERFORMANCE_OPTIMIZATION', False) else '禁用'}
    风险检查: {'启用' if context.risk_check_enabled else '禁用'}
    最大买入批次: {context.max_buy_batch}
    性能分析: {'启用' if get_config_value('ENABLE_PERFORMANCE_PROFILING', False) else '禁用'}
    性能分析阈值: {get_config_value('PROFILING_THRESHOLD_MS', 10)}ms
    性能分析汇总间隔: {get_config_value('PROFILING_SUMMARY_INTERVAL', 300)}秒
    ===============================
    """)

    # 优化性能
    optimize_performance(context)
    
    # 初始化市场波动率
    update_market_volatility(context)
    
    # 立即执行一次订阅，不等待调度
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 策略初始化时立即执行一次订阅")
    
    # 在强制回测模式下，确保首次订阅能够执行
    mode_adaptive_setting = get_config_value('ENABLE_MODE_ADAPTIVE', 0)
    if mode_adaptive_setting == 1:
        context.subscribed_today = False
        context.last_subscription_date = None
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下重置订阅状态，确保首次订阅执行")
    
    # 根据配置决定是订阅全市场还是指数成分股
    if context.subscribe_all_market:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅全市场股票")
        context.data_fetcher.subscribe_constituents('ALL')
    else:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置为订阅指数 {context.index_symbol} 成分股")
        context.data_fetcher.subscribe_constituents(context.index_symbol)

    # 🚨 数据完整性检查和修复
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 执行数据完整性检查和修复...")

    # 检查是否启用数据完整性保护
    data_integrity_config = get_config_value('DATA_INTEGRITY_CONFIG', {})
    enable_protection = data_integrity_config.get('enable_future_function_protection', True)

    if enable_protection:
        try:
            # 清理现有数据库中的未来函数数据
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔧 开始清理未来函数数据...")
            cleanup_success = clean_existing_future_function_data()

            if cleanup_success:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 未来函数数据清理完成")
            else:
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ 未来函数数据清理失败")

            # 设置数据完整性标志
            context.data_integrity_enabled = True
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 数据完整性保护已启用")

        except Exception as e:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 数据完整性检查失败: {e}")
            context.data_integrity_enabled = False
    else:
        context.data_integrity_enabled = False
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ 数据完整性保护已禁用")

    # 🎯 初始化智能评分系统
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 初始化智能评分系统...")

    try:
        # 获取评分系统实例
        context.scoring_system = get_scoring_system(context)

        # 获取系统状态
        system_status = context.scoring_system.get_system_status()

        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 智能评分系统初始化完成")
        context.log.info(f"  📊 系统状态: 启用={system_status['enabled']}")
        context.log.info(f"  📊 当前阈值: {system_status['current_threshold']}")
        context.log.info(f"  📊 历史记录: {system_status['history_count']} 条")

        if system_status['adaptive_threshold']:
            context.log.info(f"  📊 自适应阈值: {system_status['adaptive_threshold']}")

        # 设置评分系统标志
        context.smart_scoring_enabled = system_status['enabled']

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 智能评分系统初始化失败: {e}")
        context.scoring_system = None
        context.smart_scoring_enabled = False

    # 策略初始化完成后，立即同步持仓信息
    if context.run_mode == 'live':
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘模式下，立即同步持仓信息")
        try:
            # 同步API持仓到数据库
            synced_positions = context.position_manager.sync_positions()
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 同步持仓完成，当前持仓数量: {len(synced_positions)}")

            # 将数据库持仓信息更新到context
            context.position_manager.update_context_positions()
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓信息已更新到context")
        except Exception as e:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 初始化同步持仓异常: {str(e)}")

def reload_config_func(context):
    """重新加载配置的调度函数"""
    reload_config(context)

def reload_config(context):
    """重新加载配置并更新策略参数"""
    try:
        # 如果未启用动态配置，则跳过
        if not context.use_dynamic_config:
            return
            
        # 强制重新加载配置文件
        if context.config_manager.load_config():
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置已重新加载，正在更新策略参数...")
            
            # 获取最新配置
            context.config = context.config_manager.get_config()
            
            # 备份一下当前配置
            context.config_manager.save_config_backup()
            
            # 更新关键参数
            # 交易参数
            context.index_symbol = get_config_value('INDEX_SYMBOL', context.index_symbol)
            context.subscribe_all_market = get_config_value('SUBSCRIBE_ALL_MARKET', context.subscribe_all_market)
            context.max_positions = get_config_value('MAX_POSITIONS', context.max_positions)
            context.ratio = get_config_value('POSITION_RATIO', context.ratio)
            context.trailing_stop = get_config_value('TRAILING_STOP', context.trailing_stop)
            context.min_holding_days_for_trailing_stop = get_config_value('MIN_HOLDING_DAYS', context.min_holding_days_for_trailing_stop)
            context.t_plus_1 = get_config_value('T_PLUS_1', context.t_plus_1)
            
            # 更新交易执行参数
            context.use_market_order = get_config_value('USE_MARKET_ORDER', context.use_market_order)
            context.price_adjust_ratio = get_config_value('PRICE_ADJUST_RATIO', context.price_adjust_ratio)
            
            # 更新历史数据获取参数
            context.history_data_days = get_config_value('HISTORY_DATA_DAYS', context.history_data_days)
            context.history_data_days_volatility = get_config_value('HISTORY_DATA_DAYS_VOLATILITY', context.history_data_days_volatility)
            
            # 更新历史数据管理器参数
            if hasattr(context, 'history_data_manager'):
                cache_size = get_config_value('HISTORY_CACHE_SIZE', context.history_data_manager.cache_size)
                if cache_size != context.history_data_manager.cache_size:
                    context.history_data_manager.cache_size = cache_size
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新历史数据缓存大小: {cache_size}条")
            
            # 更新信号开关参数
            context.enable_buy_signals = get_config_value('ENABLE_BUY_SIGNALS', context.enable_buy_signals)
            context.enable_sell_signals = get_config_value('ENABLE_SELL_SIGNALS', context.enable_sell_signals)
            context.enable_trix_buy_signal = get_config_value('ENABLE_TRIX_BUY_SIGNAL', context.enable_trix_buy_signal)
            context.enable_ma_cross_buy_signal = get_config_value('ENABLE_MA_CROSS_BUY_SIGNAL', context.enable_ma_cross_buy_signal)
            context.enable_trailing_stop = get_config_value('ENABLE_TRAILING_STOP', context.enable_trailing_stop)
            context.enable_trix_sell_signal = get_config_value('ENABLE_TRIX_SELL_SIGNAL', context.enable_trix_sell_signal)
            context.enable_dynamic_stop_loss = get_config_value('ENABLE_DYNAMIC_STOP_LOSS', context.enable_dynamic_stop_loss)
            
            # 更新TRIX参数
            context.trix_ema_period = get_config_value('TRIX_EMA_PERIOD', context.trix_ema_period)
            context.trix_sell_ema_period = get_config_value('TRIX_SELL_EMA_PERIOD', context.trix_sell_ema_period)
            context.use_talib_trix = get_config_value('USE_TALIB_TRIX', context.use_talib_trix)
            
            # 更新动态止损参数
            context.dynamic_stop_loss_ratio = get_config_value('DYNAMIC_STOP_LOSS_RATIO', context.dynamic_stop_loss_ratio)
            
            # 更新日志设置参数
            context.enable_csv_logging = get_config_value('ENABLE_CSV_LOGGING', context.enable_csv_logging)
            context.enable_sell_signal_log = get_config_value('ENABLE_SELL_SIGNAL_LOG', True)
            context.sell_signal_log_level = get_config_value('SELL_SIGNAL_LOG_LEVEL', 'INFO')
            
            # 更新仓位管理参数
            context.single_position_limit = get_config_value('SINGLE_POSITION_LIMIT', context.single_position_limit)
            context.single_position_ratio = get_config_value('SINGLE_POSITION_RATIO', context.single_position_ratio)
            context.dynamic_position_max_ratio = get_config_value('DYNAMIC_POSITION_MAX_RATIO', context.dynamic_position_max_ratio)
            context.min_position_limit = get_config_value('MIN_POSITION_LIMIT', context.min_position_limit)
            context.min_position_ratio = get_config_value('MIN_POSITION_RATIO', context.min_position_ratio)
            # 更新买入策略使用的持仓比例参数
            context.max_position_pct = context.single_position_ratio
            context.min_position_pct = context.min_position_ratio
            
            # 更新波动性因子参数
            context.max_volatility_factor = get_config_value('MAX_VOLATILITY_FACTOR', context.max_volatility_factor)
            context.min_volatility_factor = get_config_value('MIN_VOLATILITY_FACTOR', context.min_volatility_factor)
            context.volatility_factor_scale = get_config_value('VOLATILITY_FACTOR_SCALE', context.volatility_factor_scale)
            
            # 风险管理参数
            context.risk_check_enabled = get_config_value('RISK_CHECK_ENABLED', context.risk_check_enabled)
            
            # 波动性相关参数
            context.volatility_period = get_config_value('VOLATILITY_PERIOD', context.volatility_period)
            context.volatility_threshold = get_config_value('VOLATILITY_THRESHOLD', context.volatility_threshold)
            context.atr_threshold = get_config_value('ATR_THRESHOLD', context.atr_threshold)
            
            # 性能参数
            context.volatility_cache_expire = get_config_value('CACHE_EXPIRE_SECONDS', context.volatility_cache_expire)
            
            # 更新信号检查和持仓检查频率
            old_signal_check_interval = context.signal_check_interval
            old_position_check_interval = context.position_check_interval
            context.signal_check_interval = get_config_value('SELL_SIGNAL_CHECK_INTERVAL', context.signal_check_interval)
            context.position_check_interval = get_config_value('POSITION_CHECK_INTERVAL', context.position_check_interval)
            
            # 如果频率有变化，记录日志
            if old_signal_check_interval != context.signal_check_interval:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 卖出信号检查频率已更新: {old_signal_check_interval}分钟 -> {context.signal_check_interval}分钟")
            if old_position_check_interval != context.position_check_interval:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓检查频率已更新: {old_position_check_interval}分钟 -> {context.position_check_interval}分钟")
            
            # 清空波动率缓存
            context.volatility_cache = {}
            
            # 优化性能设置
            optimize_performance(context)
            
            # 重新计算市场波动率
            update_market_volatility(context)
            
            # 更新订阅管理参数
            old_enable_mode_adaptive = getattr(context, 'enable_mode_adaptive', True)
            old_subscription_rotation_enabled = getattr(context, 'subscription_rotation_enabled', True)
            old_subscription_rotation_interval = getattr(context, 'subscription_rotation_interval', 1)
            old_live_mode_batch_size = getattr(context, 'live_mode_batch_size', 50)
            old_max_subscription_limit = getattr(context, 'max_subscription_limit', 50)
            old_max_subscription_per_batch = getattr(context, 'max_subscription_per_batch', 50)
            old_sync_buy_check_with_rotation = getattr(context, 'sync_buy_check_with_rotation', True)
            old_backtest_full_subscription = getattr(context, 'backtest_full_subscription', True)
            old_backtest_subscribe_once = getattr(context, 'backtest_subscribe_once', True)
            old_run_mode = getattr(context, 'run_mode', 'backtest')
            
            # 获取新的模式自适应设置
            mode_adaptive_setting = get_config_value('ENABLE_MODE_ADAPTIVE', 0)
            
            # 处理模式自适应设置
            if mode_adaptive_setting == 0:
                # 自动模式自适应，根据run_mode自动判断
                context.enable_mode_adaptive = True
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用自动模式自适应")
            elif mode_adaptive_setting == 1:
                # 强制使用回测模式订阅策略
                context.enable_mode_adaptive = False
                context.run_mode = 'backtest'
                context.backtest_full_subscription = True
                
                # 如果从其他模式切换到强制回测模式，重置订阅状态
                if old_run_mode != 'backtest' or not old_backtest_full_subscription:
                    context.subscribed_today = False
                    context.last_subscription_date = None
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 切换到强制回测模式，重置订阅状态")
                
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用回测模式订阅策略")
            elif mode_adaptive_setting == 2:
                # 强制使用模拟盘模式订阅策略
                context.enable_mode_adaptive = False
                context.run_mode = 'live'
                context.subscription_rotation_enabled = True
                context.live_mode_batch_size = 100  # 模拟盘使用较大批次
                context.subscription_rotation_interval = 2  # 模拟盘使用较长轮换间隔
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用模拟盘模式订阅策略")
            elif mode_adaptive_setting == 3:
                # 强制使用实盘模式订阅策略
                context.enable_mode_adaptive = False
                context.run_mode = 'live'
                context.subscription_rotation_enabled = True
                context.live_mode_batch_size = 50   # 实盘使用较小批次
                context.subscription_rotation_interval = 1  # 实盘使用较短轮换间隔
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用实盘模式订阅策略")
            else:
                # 未知设置，使用默认值
                context.enable_mode_adaptive = True
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 未知的模式自适应设置: {mode_adaptive_setting}，使用默认自动模式")
            
            # 更新其他订阅管理参数
            context.subscription_rotation_enabled = get_config_value('SUBSCRIPTION_ROTATION_ENABLED', old_subscription_rotation_enabled)
            context.subscription_rotation_interval = get_config_value('SUBSCRIPTION_ROTATION_INTERVAL', old_subscription_rotation_interval)
            context.live_mode_batch_size = get_config_value('LIVE_MODE_BATCH_SIZE', old_live_mode_batch_size)
            context.max_subscription_limit = get_config_value('MAX_SUBSCRIPTION_LIMIT', old_max_subscription_limit)
            context.max_subscription_per_batch = get_config_value('MAX_SUBSCRIPTION_PER_BATCH', old_max_subscription_per_batch)
            context.sync_buy_check_with_rotation = get_config_value('SYNC_BUY_CHECK_WITH_ROTATION', old_sync_buy_check_with_rotation)
            context.backtest_full_subscription = get_config_value('BACKTEST_FULL_SUBSCRIPTION', old_backtest_full_subscription)
            context.backtest_subscribe_once = get_config_value('BACKTEST_SUBSCRIBE_ONCE', old_backtest_subscribe_once)
            
            # 如果订阅参数有变化，更新DataFetcher中的参数
            subscription_params_changed = (
                old_enable_mode_adaptive != context.enable_mode_adaptive or
                old_subscription_rotation_enabled != context.subscription_rotation_enabled or
                old_subscription_rotation_interval != context.subscription_rotation_interval or
                old_live_mode_batch_size != context.live_mode_batch_size or
                old_max_subscription_limit != context.max_subscription_limit or
                old_max_subscription_per_batch != context.max_subscription_per_batch or
                old_sync_buy_check_with_rotation != context.sync_buy_check_with_rotation or
                old_backtest_full_subscription != context.backtest_full_subscription or
                old_backtest_subscribe_once != context.backtest_subscribe_once or
                old_run_mode != context.run_mode
            )
            
            if subscription_params_changed:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅管理参数已更新，更新DataFetcher参数")
                
                # 更新DataFetcher参数
                context.data_fetcher.enable_mode_adaptive = context.enable_mode_adaptive
                context.data_fetcher.subscription_rotation_enabled = context.subscription_rotation_enabled
                context.data_fetcher.subscription_rotation_interval = context.subscription_rotation_interval
                context.data_fetcher.live_mode_batch_size = context.live_mode_batch_size
                context.data_fetcher.max_subscription_limit = context.max_subscription_limit
                context.data_fetcher.max_subscription_per_batch = context.max_subscription_per_batch
                context.data_fetcher.sync_buy_check_with_rotation = context.sync_buy_check_with_rotation
                context.data_fetcher.backtest_full_subscription = context.backtest_full_subscription
                context.data_fetcher.backtest_subscribe_once = context.backtest_subscribe_once
                
                # 如果轮换间隔发生变化，记录日志
                if old_subscription_rotation_interval != context.subscription_rotation_interval:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅轮换间隔已更改: {old_subscription_rotation_interval}分钟 -> {context.subscription_rotation_interval}分钟")
                
                # 如果批次大小发生变化，重新初始化股票批次
                if old_live_mode_batch_size != context.live_mode_batch_size:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 批次大小已更改: {old_live_mode_batch_size} -> {context.live_mode_batch_size}，重新初始化股票批次")
                    context.data_fetcher.initialize_stock_batches(context.index_symbol, context.live_mode_batch_size)
            
            # 重新设置买入检查时间表
            # 注意：在实盘环境中，这可能会导致已经设置的调度被重复添加
            # 但由于回测环境每次reload_config时都是重新开始的，所以不会有问题
            # 实盘环境中可能需要更复杂的逻辑来取消旧的调度并添加新的调度
            buy_check_interval_mode_changed = False
            if hasattr(context.config, 'BUY_CHECK_INTERVAL_MODE'):
                old_mode = getattr(context, 'buy_check_interval_mode', False)
                # 直接从配置文件获取值，而不是使用get_config_value
                new_mode = getattr(context.config, 'BUY_CHECK_INTERVAL_MODE', False)
                context.buy_check_interval_mode = new_mode
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入检查模式配置值: {new_mode}, 之前值: {old_mode}")
                
                if old_mode != context.buy_check_interval_mode:
                    buy_check_interval_mode_changed = True
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入检查模式已更改: {'间隔模式' if context.buy_check_interval_mode else '固定时间点模式'}")
            
            if hasattr(context.config, 'BUY_CHECK_INTERVAL_MINUTES'):
                old_interval = getattr(context, 'buy_check_interval_minutes', 30)
                # 直接从配置文件获取值
                new_interval = getattr(context.config, 'BUY_CHECK_INTERVAL_MINUTES', 30)
                context.buy_check_interval_minutes = new_interval
                
                if old_interval != context.buy_check_interval_minutes and context.buy_check_interval_mode:
                    buy_check_interval_mode_changed = True
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入检查间隔已更改: {old_interval}分钟 -> {context.buy_check_interval_minutes}分钟")
            
            if hasattr(context.config, 'BUY_CHECK_TIMES') or buy_check_interval_mode_changed:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到买入检查配置变更，重新设置买入检查时间")
                setup_buy_check_schedules(context)
            
            # 重新设置持仓摘要输出时间表
            if hasattr(context.config, 'POSITION_SUMMARY_TIMES') or hasattr(context.config, 'ENABLE_POSITION_SUMMARY'):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到持仓摘要配置变更，重新设置持仓摘要输出时间")
                setup_position_summary_schedules(context)
            
            # 输出更新后的配置摘要
            context.log.info(f"""
            {context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置更新摘要:
            指数: {context.index_symbol}
            最大持仓数: {context.max_positions}
            仓位比例: {context.ratio}
            跟踪止盈阈值: {context.trailing_stop*100:.1f}%
            最小持仓天数: {context.min_holding_days_for_trailing_stop}天
            T+1交易规则: {'启用' if context.t_plus_1 else '禁用'}
            风险检查: {'启用' if context.risk_check_enabled else '禁用'}
            CSV日志写入: {'启用' if context.enable_csv_logging else '禁用'}
            
            信号检查频率:
            卖出信号检查间隔: {context.signal_check_interval}分钟
            持仓检查间隔: {context.position_check_interval}分钟
            
            信号开关设置:
            买入信号总开关: {'启用' if context.enable_buy_signals else '禁用'}
            TRIX买入信号: {'启用' if context.enable_trix_buy_signal else '禁用'}
            均线交叉买入信号: {'启用' if context.enable_ma_cross_buy_signal else '禁用'}
            卖出信号总开关: {'启用' if context.enable_sell_signals else '禁用'}
            跟踪止盈: {'启用' if context.enable_trailing_stop else '禁用'}
            TRIX死叉卖出: {'启用' if context.enable_trix_sell_signal else '禁用'}
            动态止损: {'启用' if context.enable_dynamic_stop_loss else '禁用'}
            
            TRIX指标设置:
            TRIX计算周期: {context.trix_ema_period}
            TRIX计算方式: {'使用talib.TRIX' if context.use_talib_trix else '使用自定义计算'}
            
            波动性周期: {context.volatility_period}天
            波动性阈值: {context.volatility_threshold}
            ATR阈值: {context.atr_threshold}%
            单只股票最大持仓金额: {context.single_position_limit}
            单只股票最大持仓比例: {context.single_position_ratio*100:.1f}%
            单只股票最小持仓金额: {context.min_position_limit}
            单只股票最小持仓比例: {context.min_position_ratio*100:.1f}%
            动态仓位最大比例: {context.dynamic_position_max_ratio*100:.1f}%
            波动性资金调整因子范围: {context.min_volatility_factor} - {context.max_volatility_factor}
            
            订阅管理:
            模式自适应: {'启用' if context.enable_mode_adaptive else '禁用'}
            订阅轮换: {'启用' if context.subscription_rotation_enabled else '禁用'}
            轮换间隔: {context.subscription_rotation_interval}分钟
            批次大小: {context.live_mode_batch_size}
            最大订阅限制: {context.max_subscription_limit}
            运行模式: {context.run_mode}
            订阅模式: {'batch' if context.subscription_rotation_enabled else 'full'}
            回测全量订阅: {'启用' if context.backtest_full_subscription else '禁用'}
            回测只订阅一次: {'启用' if context.backtest_subscribe_once else '禁用'}
            
            性能优化:
            性能优化总开关: {'启用' if get_config_value('ENABLE_PERFORMANCE_OPTIMIZATION', False) else '禁用'}
            风险检查: {'启用' if context.risk_check_enabled else '禁用'}
            最大买入批次: {context.max_buy_batch}
            性能分析: {'启用' if get_config_value('ENABLE_PERFORMANCE_PROFILING', False) else '禁用'}
            性能分析阈值: {get_config_value('PROFILING_THRESHOLD_MS', 10)}ms
            性能分析汇总间隔: {get_config_value('PROFILING_SUMMARY_INTERVAL', 300)}秒
            ===============================
            """)
        
        else:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 配置未变化，无需更新策略参数")
            
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 重新加载配置异常: {str(e)}")

def optimize_performance(context):
    """优化性能的函数"""
    # 检查是否启用性能优化
    enable_optimization = get_config_value('ENABLE_PERFORMANCE_OPTIMIZATION', False)
    
    if enable_optimization:
        # 禁用不必要的风险检查
        context.risk_check_enabled = get_config_value('RISK_CHECK_ENABLED', False)
        
        # 设置较大的买入批次，减少买入次数
        context.max_buy_batch = get_config_value('MAX_BUY_BATCH', 20)  # 每次最多买20只股票
        
        # 增大历史数据缓存大小，减少重复获取
        if hasattr(context, 'history_data_manager'):
            cache_size = get_config_value('HISTORY_CACHE_SIZE', 1000)  # 优化模式下默认1000条缓存
            context.history_data_manager.cache_size = cache_size
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 优化历史数据缓存大小: {cache_size}条")
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已启用优化性能设置: 风险检查={'启用' if context.risk_check_enabled else '禁用'}, 最大买入批次={context.max_buy_batch}")
    else:
        # 启用风险检查
        context.risk_check_enabled = True
        
        # 设置较小的买入批次，更谨慎地买入
        context.max_buy_batch = 10  # 每次最多买10只股票
        
        # 设置默认的历史数据缓存大小
        if hasattr(context, 'history_data_manager'):
            cache_size = get_config_value('HISTORY_CACHE_SIZE', 500)  # 普通模式下默认500条缓存
            context.history_data_manager.cache_size = cache_size
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置默认历史数据缓存大小: {cache_size}条")
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已禁用优化性能设置: 风险检查={'启用' if context.risk_check_enabled else '禁用'}, 最大买入批次={context.max_buy_batch}")

def _subscribe_holdings_only(context):
    """只订阅当前持仓的辅助函数"""
    try:
        positions = context.account().positions()
        if positions:
            holding_symbols = [p['symbol'] for p in positions]
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试只订阅当前持仓({len(holding_symbols)}只)")
            
            # 只订阅持仓
            subscribe(symbols=holding_symbols, frequency='60s', count=5)
            if hasattr(context.data_fetcher, 'constituents'):
                context.data_fetcher.constituents = holding_symbols
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功订阅{len(holding_symbols)}只持仓股票")
        else:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前没有持仓，无法订阅")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅持仓异常: {str(e)}")

@profile
def check_positions(context):
    """定期检查持仓，包括止盈止损和定期更新确认高点"""
    try:
        # 性能优化：定期检查和优化内存
        if hasattr(context, 'performance_optimizer') and context.performance_optimizer:
            context.performance_optimizer.check_and_optimize_memory()

        # 检查是否在交易时间段或回测模式
        if not (is_trading_hour(context.now.time()) or context.run_mode == 'backtest'):
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过持仓检查")
            return
            
        # 禁用风险检查时直接返回
        if not get_config_value('RISK_CHECK_ENABLED', True) and context.run_mode != 'backtest':
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 风险检查已禁用，跳过持仓检查")
            return
            
        # 通过持仓管理器获取已存储的持仓
        stored_positions = {}
        if hasattr(context, 'position_manager'):
            try:
                stored_positions = context.position_manager.get_all_positions()
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 从持仓管理器获取到{len(stored_positions)}条持仓记录")
            except Exception as e:
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取持仓管理器数据异常: {str(e)}")
        
        # 获取当前API持仓
        positions = context.account().positions()
        if not positions:
            # 检查是否启用持仓摘要
            enable_position_summary = get_config_value('ENABLE_POSITION_SUMMARY', True)
            if enable_position_summary:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前无持仓")
            else:
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前无持仓")
            return
            
        context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始检查{len(positions)}个持仓")
        
        # 更新持仓确认高点
        for position in positions:
            symbol = position['symbol']
            current_price = position['price']
            
            # 验证并获取持仓成本信息
            cost_info = validate_position_info(context, symbol, current_price)
            
            # 更新确认高点
            if current_price > cost_info['confirmed_high']:
                old_high = cost_info['confirmed_high']
                cost_info['confirmed_high'] = current_price
                cost_info['confirmed_time'] = context.now
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新{symbol}确认高点: {old_high} -> {current_price}")
        
        context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 持仓检查完成")
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检查持仓状态异常 {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

@profile
def on_bar(context, bars):
    """
    策略运行主入口，bar数据驱动
    1. 优化了处理逻辑，严格区分交易和非交易时段的行为
    2. 增加了冷却机制，避免短时间内多次尝试订阅
    3. 改进了日志记录，增加了时间戳和更详细的状态信息
    """
    try:
        # 标记当前时间所处的时间段
        current_time = context.now.time()
        is_trading = is_trading_hour(current_time)
        
        # 更新当前日期，用于检测日期变化
        current_date = context.now.date()
        
        # 如果日期变化，重置订阅状态和当日买入记录
        if current_date != context.last_date:
            context.last_date = current_date

            # 重置当日买入记录
            if hasattr(context, 'today_bought_symbols'):
                if context.today_bought_symbols:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 日期变更，清空当日买入记录: {len(context.today_bought_symbols)}只股票")
                context.today_bought_symbols.clear()
                context.last_buy_date = current_date

            # 🚀 性能优化：清理过期缓存
            try:
                if hasattr(context, 'global_data_cache') and context.global_data_cache:
                    context.global_data_cache.clear_expired_cache()

                if hasattr(context, 'trix_cache_manager') and context.trix_cache_manager:
                    context.trix_cache_manager.clear_expired_cache()

                if hasattr(context, 'batch_db_manager') and context.batch_db_manager:
                    context.batch_db_manager.check_and_flush()

                # 输出缓存统计信息
                if hasattr(context, 'global_data_cache') and context.global_data_cache:
                    stats = context.global_data_cache.get_stats()
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 缓存统计: 命中率{stats['cache_hit_rate']:.1f}%, 缓存项{stats['total_cached_items']}个")

            except Exception as cache_error:
                context.log.debug(f"缓存清理异常: {cache_error}")

            # 只在非回测模式或未启用BACKTEST_SUBSCRIBE_ONCE时重置订阅状态
            if context.run_mode != 'backtest' or not get_config_value('BACKTEST_SUBSCRIBE_ONCE', True):
                context.subscribed_today = False
                context.subscription_errors = 0  # 重置错误计数
                context.subscription_error_cooldown = False  # 重置冷却状态
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 日期变更，重置订阅状态")
            else:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式下保持订阅状态不变")
        
        # 如果进入交易时间段且今天还未订阅，执行订阅
        if is_trading and not context.subscribed_today and context.run_mode != 'backtest':
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 进入交易时间段，执行订阅")
            context.data_fetcher.subscribe_constituents(context.index_symbol)
            context.subscribed_today = True
            
        # 更新实时确认低点 - 在交易时段或回测模式下更新
        if (is_trading or context.run_mode == 'backtest') and get_config_value('ENABLE_REBOUND_BUY', False):
            update_confirmed_lows(context)
        
        # 原有的on_bar逻辑
        # 如果不在交易时间段且不是回测模式，跳过大部分处理
        if not is_trading and context.run_mode != 'backtest':
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，仅执行基本维护")
            # 不直接返回，继续执行卖出检查
        else:
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - on_bar触发，当前时间: {context.now}")
        
        # 检查是否需要轮换订阅 - 只在交易时间段或回测模式下执行
        # 回测模式下如果启用了全量订阅，则跳过轮换检查
        is_backtest_full_subscription = (context.run_mode == 'backtest' and 
                                        hasattr(context, 'backtest_full_subscription') and 
                                        context.backtest_full_subscription)
        
        if ((is_trading or context.run_mode == 'backtest') and 
            hasattr(context.data_fetcher, 'check_rotation_time') and 
            context.subscribe_rotation_seconds > 0 and 
            not is_backtest_full_subscription):
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检查是否需要轮换订阅，轮换间隔: {context.subscribe_rotation_seconds}秒")
            if context.data_fetcher.check_rotation_time():
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - on_bar中检测到需要轮换订阅")
                rotation_result = context.data_fetcher.rotate_subscription()
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅结果: {'成功' if rotation_result else '失败'}")
        elif is_backtest_full_subscription and context.subscribe_rotation_seconds > 0:
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式全量订阅，跳过轮换检查")
        
        # 根据性能优化设置控制持仓检查频率
        check_interval = context.position_check_interval * 60  # 转换为秒
        if context.last_position_check is None or (context.now - context.last_position_check).total_seconds() >= check_interval:
            check_positions(context)
            context.last_position_check = context.now

        # 获取当前持仓
        positions = context.account().positions()
        if not positions:
            return

        # 修改：有持仓时始终执行卖出信号检查，不受交易时间限制
        # 根据性能优化设置控制信号计算频率
        if context.last_signal_check_time is None or (context.now - context.last_signal_check_time).total_seconds() >= context.signal_check_interval * 60:
            # 检查是否需要跳过当日买入股票的卖出检查
            skip_today_bought = get_config_value('SKIP_TODAY_BOUGHT_SELL_CHECK', True)
            
            if skip_today_bought:
                # 获取今日买入的股票列表
                today_bought_symbols = []
                current_date_str = context.now.strftime('%Y-%m-%d')
                
                # 从持仓管理器中获取今日买入的股票
                if hasattr(context, 'position_manager'):
                    try:
                        stored_positions = context.position_manager.get_all_positions()
                        for symbol, pos_info in stored_positions.items():
                            if pos_info.get('buy_date') == current_date_str:
                                today_bought_symbols.append(symbol)
                    except Exception as e:
                        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取今日买入股票异常: {str(e)}")
                
                # 过滤掉今日买入的股票，只检查非今日买入的股票
                positions_to_check = []
                for position in positions:
                    if position['symbol'] not in today_bought_symbols:
                        positions_to_check.append(position)
                    else:
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {position['symbol']} 为今日买入，跳过卖出检查")
                
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始执行卖出检查，当前持仓数量: {len(positions)}，需要检查的持仓: {len(positions_to_check)}")
                
                # 检查卖出条件
                for position in positions_to_check:
                    _check_sell_condition(context, position)
            else:
                # 不跳过当日买入股票的卖出检查，检查所有持仓
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始执行卖出检查，当前持仓数量: {len(positions)}")
                for position in positions:
                    _check_sell_condition(context, position)
                
            context.last_signal_check_time = context.now
                
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - on_bar处理异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")

@profile
def _check_sell_condition(context, position):
    """检查单个持仓的卖出条件"""
    try:
        # 获取股票信息
        symbol = position['symbol']
        current_price = position['price']
        
        # 检查当前是否在交易时间内
        current_time = context.now.time()
        is_trading = is_trading_hour(current_time)
        if not is_trading:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 注意：在非交易时间段执行{symbol}的卖出检查，当前价格: {current_price:.3f}")
        else:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始检查 {symbol} 的卖出条件，当前价格: {current_price:.3f}")
        
        # 验证并获取持仓成本信息
        cost_info = validate_position_info(context, symbol, current_price)
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 成本信息: 买入价={cost_info['cost_price']:.3f}, 买入日期={cost_info.get('buy_date', '未知')}")

        # 检查持仓周期
        if not context.risk_manager.check_holding_period(
            cost_info.get('buy_date', cost_info.get('buy_time', context.now).strftime('%Y-%m-%d')),
            context.now.strftime('%Y-%m-%d'),
            symbol  # 添加symbol参数
        ):
            return False

        # 更新最高价 - 无论是否满足交易条件都更新最高价，用于记录
        cost_info['confirmed_high'] = max(
            cost_info['confirmed_high'],
            current_price
        )
        cost_info['confirmed_time'] = context.now
        
        # 计算持仓天数
        days_held = 0
        try:
            if isinstance(cost_info['buy_time'], datetime.datetime):
                # 确保两个日期时间对象都没有时区信息或都有相同的时区信息
                buy_date = cost_info['buy_time'].replace(tzinfo=None).date()
                current_date = context.now.replace(tzinfo=None).date()
                days_held = (current_date - buy_date).days
            elif 'buy_date' in cost_info:
                try:
                    buy_date = datetime.datetime.strptime(cost_info['buy_date'], '%Y-%m-%d').date()
                    current_date = context.now.replace(tzinfo=None).date()
                    days_held = (current_date - buy_date).days
                except Exception as e:
                    days_held = 0
                    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 无法解析buy_date: {cost_info['buy_date']}, 错误: {str(e)}")
        except Exception as e:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 计算持仓天数异常: {str(e)}")
        
        # 检查卖出条件
        sell_signals = context.signal_generator.check_sell_conditions(position, cost_info)
        
        # 如果有卖出信号，记录并返回True
        if sell_signals:
            # 将字典类型的卖出信号转换为卖出原因列表
            sell_reasons = []
            if sell_signals.get('trailing_stop', False):
                sell_reasons.append('跟踪止盈')
            if sell_signals.get('fixed_profit_stop', False):
                sell_reasons.append('固定止盈')
            if sell_signals.get('trix_death_cross', False):
                sell_reasons.append('TRIX死叉')
            if sell_signals.get('dynamic_stop_loss', False):
                sell_reasons.append('动态止损')
            if sell_signals.get('fixed_stop_loss', False):
                sell_reasons.append('固定止损')
            if sell_signals.get('max_holding_days', False):
                sell_reasons.append('最大持仓天数')
            if sell_signals.get('time_stop_loss', False):
                sell_reasons.append('时间止损')
            
            # 如果没有任何具体的卖出信号，则返回False
            if not sell_reasons:
                return False
            
            # 获取卖出信号优先级
            sell_signal_priority = get_config_value('SELL_SIGNAL_PRIORITY', {
                'trailing_stop': 1,       # 跟踪止盈优先级最高
                'fixed_profit_stop': 1.5, # 固定止盈优先级次之
                'dynamic_stop_loss': 2,   # 动态止损再次之
                'trix_death_cross': 3,     # TRIX死叉优先级最低
                'max_holding_days': 2.5   # 最大持仓天数优先级
            })
            
            # 根据优先级排序卖出原因
            priority_map = {
                '跟踪止盈': sell_signal_priority.get('trailing_stop', 1),
                '固定止盈': sell_signal_priority.get('fixed_profit_stop', 1.3),
                'TRIX死叉': sell_signal_priority.get('trix_death_cross', 3),
                '动态止损': sell_signal_priority.get('dynamic_stop_loss', 1.8),
                '固定止损': sell_signal_priority.get('fixed_stop_loss', 1.6),
                '时间止损': sell_signal_priority.get('time_stop_loss', 2.2),
                '最大持仓天数': sell_signal_priority.get('max_holding_days', 2.5)
            }
            
            # 按优先级排序（数字越小优先级越高）
            sell_reasons.sort(key=lambda x: priority_map.get(x, 999))
                
            # 取优先级最高的卖出原因
            reason = sell_reasons[0]
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 触发卖出信号: {reason} (优先级: {priority_map.get(reason, 999)})")
            
            # 计算收益率
            profit_pct = (current_price - cost_info['cost_price']) / cost_info['cost_price'] * 100
            
            # 记录交易
            context.trade_executor.log_trade(symbol, reason, cost_info, position)
            
            # 如果不在交易时间内，记录日志但不执行卖出
            if not is_trading and context.run_mode != 'backtest':
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 触发卖出信号({reason})，但当前不在交易时间段，将在下一个交易时段执行")
                # 将卖出信息保存到context中，以便在下一个交易时段执行
                if not hasattr(context, 'pending_sells'):
                    context.pending_sells = {}
                context.pending_sells[symbol] = {
                    'reason': reason,
                    'volume': position['volume'],
                    'detected_time': context.now.strftime('%Y-%m-%d %H:%M:%S')
                }
                return False
            
            # 执行卖出
            result = context.trade_executor.execute_order(
                symbol=symbol,
                volume=position['volume'],
                price=current_price,
                side=OrderSide_Sell,
                position_effect=PositionEffect_Close
            )

            # 如果卖出成功，记录卖出交易到数据库
            if result:
                try:
                    # 获取交易日志数据
                    trade_log_data = context.trade_executor.log_trade(symbol, reason, cost_info, position)

                    # 创建卖出交易记录
                    sell_trade_data = {
                        'Timestamp': context.now.strftime('%Y-%m-%d %H:%M:%S%z'),
                        'Symbol': symbol,
                        'Action': 'SELL',  # 设置为SELL
                        'Price': current_price,
                        'Volume': position['volume'],
                        'Sell_Reason': reason,
                        'Cost_Price_Sell': cost_info['cost_price'],
                        'Net_Profit_Pct_Sell': trade_log_data.get('net_profit_pct', 0),
                        'Confirmed_High_Sell': cost_info.get('confirmed_high', current_price),
                        'Confirmed_High_Time': cost_info.get('confirmed_time', context.now).strftime('%Y-%m-%d %H:%M:%S'),
                        'Holding_Hours': trade_log_data.get('holding_days', 0) * 24,
                        'Max_Profit_Pct': trade_log_data.get('max_profit_pct', 0),
                        'Final_Drawdown_Pct': trade_log_data.get('final_drawdown_pct', 0)
                    }

                    # 保存卖出记录到数据库
                    data_manager.save_trade(sell_trade_data)
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已保存卖出交易记录: {symbol}")

                except Exception as e:
                    context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 保存卖出交易记录异常: {str(e)}")
            
            if result:
                # 更新持仓
                if symbol in context.positions:
                    del context.positions[symbol]
                if symbol in context.positions_cost:
                    del context.positions_cost[symbol]
                    
                # 🎯 更新智能评分系统性能数据
                if (hasattr(context, 'scoring_system') and
                    context.scoring_system and
                    hasattr(context, 'smart_scoring_enabled') and
                    context.smart_scoring_enabled):

                    try:
                        # 计算收益率
                        buy_price = cost_info.get('cost_price', current_price)
                        if buy_price > 0:
                            return_pct = (current_price - buy_price) / buy_price * 100

                            # 尝试从数据库获取买入时的评分
                            buy_score = get_buy_score_from_db(symbol, cost_info.get('buy_date'))

                            if buy_score is not None:
                                # 更新评分系统性能
                                context.scoring_system.update_performance(buy_score, return_pct)
                                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 更新智能评分性能: 评分{buy_score:.1f}, 收益{return_pct:.2f}%")

                    except Exception as scoring_e:
                        context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 更新智能评分性能失败: {scoring_e}")

                # 记录卖出信息
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 卖出订单已成交，已清空持仓")
                return True
            else:
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 卖出执行失败")
                return False
        
        return False
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检查卖出条件异常 - {symbol}: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
        return False

def on_error(context, code, info):
    """
    处理错误信息，特别是订阅失败的情况
    
    修改说明：
    1. 优化了非交易时段的处理，不再尝试重新订阅
    2. 增强了冷却机制，避免短时间内多次尝试订阅
    3. 保持使用配置的批次大小，不自动减小批次大小
    """
    try:
        now = context.now
        context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 发生错误: code={code}, info={info}")
        
        # 处理订阅失败错误
        if "订阅代码数量超过用户权限" in info or ("subscribe" in info.lower() and "fail" in info.lower()):
            # 检查是否在交易时间段
            is_trading = is_trading_hour(now.time())
            
            # 检查错误冷却状态
            if context.subscription_error_cooldown:
                # 如果在冷却期，检查是否已过冷却时间（10分钟）
                if context.last_subscription_error_time is not None and (now - context.last_subscription_error_time).total_seconds() < 600:
                    context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅错误冷却期内，跳过处理")
                    return
                else:
                    # 冷却期结束
                    context.subscription_error_cooldown = False
                    context.subscription_errors = 0  # 重置错误计数
                    context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 冷却期结束，重置错误计数")
            
            # 增加错误计数
            context.subscription_errors += 1
            context.last_subscription_error_time = now
            
            # 如果短时间内错误过多，进入冷却期
            if context.subscription_errors >= 3:
                context.subscription_error_cooldown = True
                context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅错误次数过多({context.subscription_errors}次)，进入10分钟冷却期")
                # 只订阅持仓，不尝试全量订阅
                _subscribe_holdings_only(context)
                return
            
            context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到订阅失败，可能超过订阅限制")
            
            # 非交易时段处理逻辑
            if not is_trading and context.run_mode != 'backtest':
                context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，不进行订阅重试")
                # 只记录日志，不尝试重新订阅
                return
            
            # 交易时段或回测模式的处理逻辑
            # 先尝试取消所有可能的订阅
            try:
                # 获取沪深300成分股
                context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试取消所有沪深300成分股的订阅")
                # 使用data_fetcher.get_index_stocks替代stk_get_index_constituents
                if hasattr(context.data_fetcher, 'get_index_stocks'):
                    constituents = context.data_fetcher.get_index_stocks(context.index_symbol)
                    if constituents:
                        all_symbols = constituents
                        try:
                            unsubscribe(symbols=all_symbols)
                            context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 已尝试取消所有沪深300成分股的订阅")
                        except Exception as e:
                            context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 取消所有订阅时发生错误: {str(e)}")
                else:
                    context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - data_fetcher没有get_index_stocks方法，无法获取指数成分股")
                
                # 确保取消当前已知的订阅
                if hasattr(context.data_fetcher, 'constituents') and context.data_fetcher.constituents:
                    try:
                        unsubscribe(symbols=context.data_fetcher.constituents)
                        context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 已取消当前已知的{len(context.data_fetcher.constituents)}只股票订阅")
                        # 清空已订阅列表
                        context.data_fetcher.constituents = []
                    except Exception as e:
                        context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 取消已知订阅时发生错误: {str(e)}")
                
                # 等待一小段时间，确保订阅系统有时间处理取消操作
                time.sleep(2)
            except Exception as e:
                context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试取消所有订阅时发生异常: {str(e)}")
            
            # 获取批次大小 - 修复属性名称问题
            batch_size = None
            if hasattr(context.data_fetcher, 'live_mode_batch_size'):
                batch_size = context.data_fetcher.live_mode_batch_size
                context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 获取到批次大小: {batch_size}")
            else:
                # 从配置中获取批次大小
                batch_size = get_config_value('LIVE_MODE_BATCH_SIZE', 20)
                context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 从配置中获取批次大小: {batch_size}")
            
            # 确保批次大小有效
            if batch_size is not None and batch_size > 0:
                # 检查是否需要立即重试订阅
                if context.subscription_errors > 1:
                    context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 短时间内多次订阅失败，等待下次轮换订阅")
                    return
                
                # 尝试重新订阅
                context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 使用批次大小{batch_size}重新订阅")
                try:
                    # 先等待一小段时间，避免频繁订阅
                    time.sleep(1)
                    
                    # 重新初始化批次
                    if hasattr(context.data_fetcher, 'initialize_stock_batches'):
                        # 使用配置的批次大小
                        smaller_batch_size = batch_size
                        context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 使用配置的批次大小({smaller_batch_size})重新初始化批次")
                        context.data_fetcher.initialize_stock_batches(context.index_symbol, smaller_batch_size)
                        
                        # 重新轮换订阅
                        success = context.data_fetcher.rotate_subscription(context.index_symbol)
                        context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 重新订阅结果: {'成功' if success else '失败'}")
                    else:
                        context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - data_fetcher没有initialize_stock_batches方法")
                        # 尝试直接使用subscribe_constituents方法
                        if hasattr(context.data_fetcher, 'subscribe_constituents'):
                            success = context.data_fetcher.subscribe_constituents(context.index_symbol)
                            context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 重新订阅结果: {'成功' if success else '失败'}")
                        else:
                            context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - data_fetcher没有subscribe_constituents方法")
                except Exception as e:
                    context.log.error(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 重新订阅异常: {str(e)}")
                    # 订阅失败时，尝试只订阅持仓
                    _subscribe_holdings_only(context)
            else:
                context.log.warning(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取有效的批次大小")
                # 批次大小无效时，尝试只订阅持仓
                _subscribe_holdings_only(context)
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 处理错误信息异常: {str(e)}")
        # 发生异常时，尝试只订阅持仓
        try:
            _subscribe_holdings_only(context)
        except:
            pass

def on_strategy_finished(context, indicator):
    """策略结束时调用"""
    try:
        context.log.info(f"策略执行结束，最终账户价值: {context.account().cash.nav}")
        
        # 输出持仓信息
        check_positions(context)
        
        # 输出历史数据缓存统计信息
        if hasattr(context, 'history_data_manager'):
            context.history_data_manager.print_cache_stats()
        
        # 如果启用了性能分析，输出最终的性能分析结果
        if hasattr(context, 'profiler') and context.profiler:
            context.log.info("输出最终性能分析结果")
            context.profiler.output_summary(force_output=True)
    except Exception as e:
        context.log.error(f"策略结束处理异常: {str(e)}")

@profile
def calculate_volatility(context, symbol, period=None):
    """计算股票的波动性和ATR"""
    if period is None:
        period = get_config_value('VOLATILITY_PERIOD', 20)
        
    try:
        # 获取历史数据 - 使用历史数据管理器优化性能
        fields_list = ['symbol', 'open', 'high', 'low', 'close']  # 明确使用列表格式
        
        if hasattr(context, 'history_data_manager'):
            # 使用优化的历史数据管理器获取数据
            hist_data = context.history_data_manager.get_history_data(
                symbol=symbol,
                frequency='1d',
                count=context.history_data_days_volatility,  # 使用波动性计算专用的历史数据天数
                fields=fields_list  # 使用列表格式
            )
        else:
            # 兼容模式：使用原始data_fetcher获取数据
            hist_data = context.data_fetcher.get_history_data(
                symbol=symbol,
                frequency='1d',
                count=context.history_data_days_volatility,  # 使用波动性计算专用的历史数据天数
                fields=fields_list  # 使用列表格式
            )
        
        if hist_data is None or hist_data.empty:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取 {symbol} 的历史数据，无法计算波动性")
            return 0, 0, False
        
        # 新增：验证并修复数据格式
        required_columns = ['open', 'high', 'low', 'close']
        
        # 检查是否有合并的列名(如'symbol,open,high,low,close')
        merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
        if merged_columns:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到 {symbol} 的历史数据有合并列名: {merged_columns}")
            
            # 如果有history_data_manager，使用其修复方法
            if hasattr(context, 'history_data_manager'):
                hist_data = context.history_data_manager._fix_merged_columns(hist_data, symbol)
            else:
                # 尝试修复合并的列名
                for merged_col in merged_columns:
                    # 分割合并的列名
                    split_cols = [c.strip() for c in merged_col.split(',')]
                    
                    # 如果数据是一列，但列名包含多个字段，尝试分割数据
                    if len(hist_data.columns) == 1:
                        try:
                            # 检查第一行数据是否为字符串且包含逗号
                            first_row = hist_data.iloc[0, 0]
                            if isinstance(first_row, str) and ',' in first_row:
                                # 数据也是逗号分隔的，需要完全重新解析
                                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试重新解析 {symbol} 的历史数据")
                                
                                # 将每行数据分割成多列
                                new_data = []
                                for i in range(len(hist_data)):
                                    row_values = hist_data.iloc[i, 0].split(',')
                                    if len(row_values) == len(split_cols):
                                        new_data.append([v.strip() for v in row_values])
                                
                                # 创建新的DataFrame
                                hist_data = pd.DataFrame(new_data, columns=split_cols)
                                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功重新解析 {symbol} 的历史数据")
                            else:
                                # 只有列名有问题，数据本身格式正确
                                # 重命名列
                                hist_data.columns = split_cols
                        except Exception as e:
                            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试修复 {symbol} 的历史数据格式失败: {str(e)}")
                            return 0, 0, False
        
        # 再次检查必要的列是否存在
        missing_columns = [col for col in required_columns if col not in hist_data.columns]
        if missing_columns:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据缺少必要列: {missing_columns}")
            return 0, 0, False
            
        # 确保数据足够
        if len(hist_data) < period:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据不足{period}天，无法准确计算波动性")
            return 0, 0, False
            
        # 确保数值类型正确
        for col in required_columns:
            try:
                hist_data[col] = hist_data[col].astype(float)
            except Exception as e:
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据列 {col} 无法转换为数值类型: {str(e)}")
                return 0, 0, False
            
        # 计算日收益率
        hist_data['daily_return'] = hist_data['close'].pct_change() * 100
        
        # 计算波动率（收益率的标准差）
        volatility = hist_data['daily_return'].tail(period).std()
        
        # 计算ATR（平均真实波幅）
        hist_data['tr1'] = abs(hist_data['high'] - hist_data['low'])
        hist_data['tr2'] = abs(hist_data['high'] - hist_data['close'].shift(1))
        hist_data['tr3'] = abs(hist_data['low'] - hist_data['close'].shift(1))
        hist_data['tr'] = hist_data[['tr1', 'tr2', 'tr3']].max(axis=1)
        atr = hist_data['tr'].tail(period).mean()
        atr_pct = atr / hist_data['close'].iloc[-1] * 100  # ATR占收盘价的百分比
        
        # 获取市场平均波动率
        market_volatility = context.market_volatility if hasattr(context, 'market_volatility') else get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
        
        # 判断波动性是否足够大
        # 1. 个股波动率高于市场平均波动率配置的倍数
        # 2. 或者ATR占收盘价的比例超过配置的阈值
        # 3. 或者波动率本身超过配置的绝对阈值
        volatility_threshold = get_config_value('VOLATILITY_THRESHOLD', 1.0)
        atr_threshold = get_config_value('ATR_THRESHOLD', 1.5)
        min_absolute_volatility = get_config_value('MIN_ABSOLUTE_VOLATILITY', 1.8)
        
        is_volatile = (volatility > market_volatility * volatility_threshold) or \
                     (atr_pct > atr_threshold) or \
                     (volatility > min_absolute_volatility)
        
        return volatility, atr_pct, is_volatile
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算 {symbol} 波动性异常 {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
        return 0, 0, False

@profile
def calculate_market_volatility(context, period=None):
    """计算市场波动率"""
    if period is None:
        period = get_config_value('VOLATILITY_PERIOD', 20)
        
    try:
        # 获取沪深00指数历史数据 - 使用历史数据管理器优化性能
        fields_list = ['symbol', 'close']  # 明确使用列表格式
        
        if hasattr(context, 'history_data_manager'):
            # 使用优化的历史数据管理器获取数据
            hist_data = context.history_data_manager.get_history_data(
                symbol=context.index_symbol,  # 沪深00指数
                frequency='1d',
                count=context.history_data_days_volatility,  # 使用波动性计算专用的历史数据天数
                fields=fields_list  # 使用列表格式
            )
        else:
            # 兼容模式：使用原始data_fetcher获取数据
            hist_data = context.data_fetcher.get_history_data(
                symbol=context.index_symbol,  # 沪深00指数
                frequency='1d',
                count=context.history_data_days_volatility,  # 使用波动性计算专用的历史数据天数
                fields=fields_list  # 使用列表格式，修改为统一格式
            )
        
        default_market_volatility = get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
        
        if hist_data is None or hist_data.empty:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取市场指数历史数据，使用默认波动率{default_market_volatility}%")
            return default_market_volatility
        
        # 新增：验证并修复数据格式
        required_columns = ['close']
        
        # 检查是否有合并的列名(如'symbol,close')
        merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
        if merged_columns:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到市场指数历史数据有合并列名: {merged_columns}")
            
            # 如果有history_data_manager，使用其修复方法
            if hasattr(context, 'history_data_manager'):
                hist_data = context.history_data_manager._fix_merged_columns(hist_data, context.index_symbol)
            else:
                # 尝试修复合并的列名
                for merged_col in merged_columns:
                    # 分割合并的列名
                    split_cols = [c.strip() for c in merged_col.split(',')]
                    
                    # 如果数据是一列，但列名包含多个字段，尝试分割数据
                    if len(hist_data.columns) == 1:
                        try:
                            # 检查第一行数据是否为字符串且包含逗号
                            first_row = hist_data.iloc[0, 0]
                            if isinstance(first_row, str) and ',' in first_row:
                                # 数据也是逗号分隔的，需要完全重新解析
                                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试重新解析市场指数历史数据")
                                
                                # 将每行数据分割成多列
                                new_data = []
                                for i in range(len(hist_data)):
                                    row_values = hist_data.iloc[i, 0].split(',')
                                    if len(row_values) == len(split_cols):
                                        new_data.append([v.strip() for v in row_values])
                                
                                # 创建新的DataFrame
                                hist_data = pd.DataFrame(new_data, columns=split_cols)
                                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功重新解析市场指数历史数据")
                            else:
                                # 只有列名有问题，数据本身格式正确
                                # 重命名列
                                hist_data.columns = split_cols
                        except Exception as e:
                            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试修复市场指数历史数据格式失败: {str(e)}")
                            return default_market_volatility
        
        # 再次检查必要的列是否存在
        missing_columns = [col for col in required_columns if col not in hist_data.columns]
        if missing_columns:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 市场指数历史数据缺少必要列: {missing_columns}")
            return default_market_volatility
            
        # 确保数据足够
        if len(hist_data) < period:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 市场指数历史数据不足 {period} 天，使用默认波动{default_market_volatility}%")
            return default_market_volatility
        
        # 确保数值类型正确
        try:
            hist_data['close'] = hist_data['close'].astype(float)
        except Exception as e:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 市场指数历史数据列 close 无法转换为数值类型: {str(e)}")
            return default_market_volatility
            
        # 计算日收益率
        hist_data['daily_return'] = hist_data['close'].pct_change() * 100
        
        # 计算波动率（收益率的标准差）
        market_volatility = hist_data['daily_return'].tail(period).std()
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前市场波动率 {market_volatility:.2f}%")
        return market_volatility
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算市场波动率异常 {str(e)}")
        return get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)  # 返回默认值

def update_market_volatility(context):
    """更新市场波动率"""
    try:
        # 检查index_symbol是否为None
        if context.index_symbol is None:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 参考指数为None，无法获取市场波动率，使用默认值")
            context.market_volatility = get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
            context.last_market_volatility_update = context.now
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新市场波动率 {context.market_volatility:.2f}%")
            return
            
        context.market_volatility = calculate_market_volatility(context)
        context.last_market_volatility_update = context.now
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新市场波动率 {context.market_volatility:.2f}%")
        
        # 清空波动率缓存
        context.volatility_cache = {}
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新市场波动率异常 {str(e)}")

def is_trading_hour(current_time=None):
    """
    判断当前是否在交易时间段内
    
    Args:
        current_time: 指定时间，如果为None则使用当前时间
        
    Returns:
        bool: 是否在交易时间段内
    """
    if current_time is None:
        current_time = datetime.datetime.now().time()
    elif isinstance(current_time, datetime.datetime):
        current_time = current_time.time()
        
    # 上午交易时段: 9:30 - 11:30
    morning_session = (
        datetime.time(9, 30) <= current_time <= datetime.time(11, 30)
    )
    
    # 下午交易时段: 13:00 - 15:00
    afternoon_session = (
        datetime.time(13, 0) <= current_time <= datetime.time(15, 0)
    )
    
    return morning_session or afternoon_session

def is_preparation_hour(current_time=None):
    """
    判断当前是否在交易准备时间段内（9:00-9:30）
    
    Args:
        current_time: 指定时间，如果为None则使用当前时间
        
    Returns:
        bool: 是否在交易准备时间段内
    """
    if current_time is None:
        current_time = datetime.datetime.now().time()
    elif isinstance(current_time, datetime.datetime):
        current_time = current_time.time()
        
    # 交易准备时段: 9:00 - 9:30
    preparation_session = (
        datetime.time(9, 0) <= current_time < datetime.time(9, 30)
    )
    
    return preparation_session

def validate_position_info(context, symbol, current_price=None):
    """
    验证并修复持仓信息，确保所有必要字段都存在
    
    参数:
    - context: 策略上下文
    - symbol: 股票代码
    - current_price: 当前价格(可选)
    
    返回:
    - dict: 验证并修复后的持仓信息
    """
    try:
        # 如果当前价格未提供，尝试获取
        if current_price is None:
            try:
                current_data = current(symbols=symbol)
                if current_data and len(current_data) > 0:
                    current_price = current_data[0]['price']
                else:
                    # 如果无法获取当前价格，使用默认值
                    current_price = 0
            except Exception as e:
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取{symbol}当前价格异常: {str(e)}")
                current_price = 0
        
        # 检查持仓成本字典是否存在
        if not hasattr(context, 'positions_cost'):
            context.positions_cost = {}
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 创建positions_cost字典")
        
        # 检查该股票的持仓信息是否存在
        if symbol not in context.positions_cost:
            # 创建新的持仓信息
            context.positions_cost[symbol] = {
                'symbol': symbol,
                'cost_price': current_price,
                'buy_time': context.now.replace(tzinfo=None),
                'buy_date': context.now.strftime('%Y-%m-%d'),
                'confirmed_high': current_price,
                'confirmed_time': context.now.replace(tzinfo=None)
            }
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 创建{symbol}的持仓信息")
            return context.positions_cost[symbol]
        
        # 检查持仓信息是否为字典类型
        if not isinstance(context.positions_cost[symbol], dict):
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol}的持仓信息不是字典类型: {type(context.positions_cost[symbol])}")
            # 重置为正确的字典格式
            context.positions_cost[symbol] = {
                'symbol': symbol,
                'cost_price': current_price,
                'buy_time': context.now.replace(tzinfo=None),
                'buy_date': context.now.strftime('%Y-%m-%d'),
                'confirmed_high': current_price,
                'confirmed_time': context.now.replace(tzinfo=None)
            }
            return context.positions_cost[symbol]
        
        # 获取持仓信息
        cost_info = context.positions_cost[symbol]
        
        # 检查并添加缺失的必要字段
        required_fields = {
            'symbol': symbol,
            'cost_price': current_price,
            'buy_time': context.now.replace(tzinfo=None),
            'buy_date': context.now.strftime('%Y-%m-%d'),
            'confirmed_high': current_price,
            'confirmed_time': context.now.replace(tzinfo=None)
        }
        
        # 检查每个必要字段
        for field, default_value in required_fields.items():
            if field not in cost_info:
                cost_info[field] = default_value
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 添加{symbol}缺失的{field}字段")
        
        # 确保buy_time是datetime类型
        if not isinstance(cost_info['buy_time'], datetime.datetime):
            try:
                # 尝试从buy_date转换
                if 'buy_date' in cost_info and isinstance(cost_info['buy_date'], str):
                    cost_info['buy_time'] = datetime.datetime.strptime(cost_info['buy_date'], '%Y-%m-%d')
                else:
                    # 使用当前时间，但去除时区信息以保持一致性
                    cost_info['buy_time'] = context.now.replace(tzinfo=None)
            except Exception as e:
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 转换{symbol}的buy_time异常: {str(e)}")
                cost_info['buy_time'] = context.now.replace(tzinfo=None)
        else:
            # 确保已有的buy_time没有时区信息
            cost_info['buy_time'] = cost_info['buy_time'].replace(tzinfo=None)
        
        # 确保confirmed_time是datetime类型
        if not isinstance(cost_info['confirmed_time'], datetime.datetime):
            cost_info['confirmed_time'] = context.now.replace(tzinfo=None)
        else:
            # 确保已有的confirmed_time没有时区信息
            cost_info['confirmed_time'] = cost_info['confirmed_time'].replace(tzinfo=None)
        
        return cost_info
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 验证{symbol}持仓信息异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
        
        # 返回安全的默认值
        return {
            'symbol': symbol,
            'cost_price': current_price if current_price is not None else 0,
            'buy_time': context.now.replace(tzinfo=None),
            'buy_date': context.now.strftime('%Y-%m-%d'),
            'confirmed_high': current_price if current_price is not None else 0,
            'confirmed_time': context.now.replace(tzinfo=None)
        }

def update_confirmed_lows(context, symbols=None):
    """更新股票的确认低点"""
    try:
        # 检查反弹买入策略是否启用，如果未启用则不更新最低点
        if not get_config_value('ENABLE_REBOUND_BUY', False):
            return
            
        if symbols is None:
            # 如果没有指定股票，使用当前订阅的所有股票
            if hasattr(context.data_fetcher, 'constituents'):
                symbols = context.data_fetcher.constituents
            else:
                return
                
        current_time = context.now
        rebound_period = get_config_value('REBOUND_PERIOD', 5)  # 监控期
        
        # 删除超过监控期的低点记录
        for symbol in list(context.confirmed_lows.keys()):
            if symbol in context.confirmed_lows:
                low_time = context.confirmed_lows[symbol].get('time')
                if low_time and (current_time - low_time).days > rebound_period:
                    del context.confirmed_lows[symbol]
                    context.log.debug(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 确认低点已超过{rebound_period}天监控期，已删除")
        
        # 更新当前股票的确认低点
        for symbol in symbols:
            try:
                # 获取当前价格
                current_data = current(symbols=symbol)
                if not current_data or len(current_data) == 0:
                    continue
                    
                current_price = current_data[0]['price']
                
                # 如果股票没有确认低点记录或当前价格低于确认低点，更新记录
                if (symbol not in context.confirmed_lows) or (current_price < context.confirmed_lows[symbol]['price']):
                    old_low = context.confirmed_lows.get(symbol, {}).get('price', '无')
                    context.confirmed_lows[symbol] = {
                        'price': current_price,
                        'time': current_time
                    }
                    context.log.info(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 更新确认低点: {old_low} -> {current_price}")
            except Exception as e:
                context.log.error(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')} - 更新{symbol}确认低点异常: {str(e)}")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新确认低点函数异常: {str(e)}")

@profile
def calculate_amplitude(context, symbol, days=None):
    """
    计算股票在指定天数内的振幅
    
    参数:
    context: 策略上下文
    symbol: 股票代码
    days: 计算天数，默认为None，将从配置中读取
    
    返回:
    float: 振幅百分比
    bool: 是否满足最小振幅要求
    """
    try:
        # 从配置中获取参数
        if days is None:
            days = get_config_value('AMPLITUDE_DAYS', 15)
        min_amplitude_pct = get_config_value('MIN_AMPLITUDE_PCT', 10.0)
        
        # 获取历史数据
        fields_list = ['symbol', 'high', 'low']
        
        if hasattr(context, 'history_data_manager'):
            # 使用优化的历史数据管理器获取数据
            hist_data = context.history_data_manager.get_history_data(
                symbol=symbol,
                frequency='1d',
                count=days,
                fields=fields_list
            )
        else:
            # 兼容模式：使用原始data_fetcher获取数据
            hist_data = context.data_fetcher.get_history_data(
                symbol=symbol,
                frequency='1d',
                count=days,
                fields=fields_list
            )
        
        if hist_data is None or hist_data.empty:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取 {symbol} 的历史数据，无法计算振幅")
            return 0, False
        
        # 确保数据格式正确
        if 'high' not in hist_data.columns or 'low' not in hist_data.columns:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 历史数据缺少high或low列")
            return 0, False
        
        # 确保数值类型正确
        hist_data['high'] = hist_data['high'].astype(float)
        hist_data['low'] = hist_data['low'].astype(float)
        
        # 计算期间最高价和最低价
        highest_price = hist_data['high'].max()
        lowest_price = hist_data['low'].min()
        
        # 计算振幅
        if lowest_price > 0:
            amplitude_pct = (highest_price - lowest_price) / lowest_price * 100
        else:
            amplitude_pct = 0
            
        # 判断是否满足最小振幅要求
        meets_requirement = amplitude_pct >= min_amplitude_pct
        
        return amplitude_pct, meets_requirement
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算 {symbol} 振幅异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
        return 0, False

@profile
def calculate_ma_direction(context, symbol, ma_period=None, days=None):
    """
    计算股票均线的方向
    
    参数:
    context: 策略上下文
    symbol: 股票代码
    ma_period: 均线周期，默认为None，将从配置中读取
    days: 判断方向的天数，默认为None，将从配置中读取
    
    返回:
    str: 均线方向，'up'表示向上，'down'表示向下，'flat'表示平稳
    bool: 是否符合配置中指定的方向要求
    float: 当前均线值
    """
    try:
        # 从配置中获取参数
        if ma_period is None:
            ma_period = get_config_value('MA_FILTER_PERIOD', 5)
        if days is None:
            days = get_config_value('MA_FILTER_DAYS', 3)
        ma_direction = get_config_value('MA_FILTER_DIRECTION', 'up')
        
        # 获取历史数据
        fields_list = ['symbol', 'close']
        
        if hasattr(context, 'history_data_manager'):
            # 使用优化的历史数据管理器获取数据
            hist_data = context.history_data_manager.get_history_data(
                symbol=symbol,
                frequency='1d',
                count=ma_period + days,  # 需要额外的天数来计算均线方向
                fields=fields_list
            )
        else:
            # 兼容模式：使用原始data_fetcher获取数据
            hist_data = context.data_fetcher.get_history_data(
                symbol=symbol,
                frequency='1d',
                count=ma_period + days,
                fields=fields_list
            )
        
        if hist_data is None or hist_data.empty:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取 {symbol} 的历史数据，无法计算均线方向")
            return 'unknown', False, 0
        
        # 确保数据格式正确
        if 'close' not in hist_data.columns:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 历史数据缺少close列")
            return 'unknown', False, 0
        
        # 确保数值类型正确
        hist_data['close'] = hist_data['close'].astype(float)
        
        # 计算均线
        hist_data[f'ma{ma_period}'] = hist_data['close'].rolling(window=ma_period).mean()
        
        # 去除NaN值
        hist_data = hist_data.dropna()
        
        if len(hist_data) < days + 1:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 历史数据不足，无法判断均线方向")
            return 'unknown', False, 0
        
        # 获取最近几天的均线值
        ma_values = hist_data[f'ma{ma_period}'].tail(days + 1).values
        
        # 计算均线斜率
        ma_slopes = []
        for i in range(1, len(ma_values)):
            ma_slopes.append(ma_values[i] - ma_values[i-1])
        
        # 判断均线方向
        positive_slopes = sum(slope > 0 for slope in ma_slopes)
        negative_slopes = sum(slope < 0 for slope in ma_slopes)
        
        # 确定均线方向
        if positive_slopes >= len(ma_slopes) * 0.7:
            direction = 'up'
        elif negative_slopes >= len(ma_slopes) * 0.7:
            direction = 'down'
        else:
            direction = 'flat'
        
        # 判断是否符合配置中指定的方向要求
        is_direction_match = (direction == ma_direction)
        
        # 返回均线方向、是否符合要求和当前均线值
        return direction, is_direction_match, ma_values[-1]
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算 {symbol} 均线方向异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
        return 'unknown', False, 0

if __name__ == '__main__':
    import sys
    import os
    # 使用配置管理器而不是直接导入config
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='万和交易策略')
    parser.add_argument('--disable-analysis-log', action='store_true', help='禁用分析日志输出')
    parser.add_argument('--disable-dynamic-config', action='store_true', help='禁用动态配置功能')
    parser.add_argument('--config-check-interval', type=int, help='配置检查间隔(秒)')
    # 添加平台可能传递的参数，使用ignore_unknown=True忽略其他未知参数
    parser.add_argument('--strategy_id', help='策略ID')
    parser.add_argument('--token', help='访问令牌')
    parser.add_argument('--mode', type=int, help='运行模式')
    parser.add_argument('--serv_addr', help='服务器地址')
    
    # 解析已知参数，忽略未知参数
    args, unknown = parser.parse_known_args()
    
    # 设置是否启用分析日志
    if args.disable_analysis_log:
        print("已禁用分析日志输出")
        # 通过环境变量传递参数给策略
        os.environ['ENABLE_ANALYSIS_LOG'] = '0'
    else:
        print("已启用分析日志输出")
        os.environ['ENABLE_ANALYSIS_LOG'] = '1'
    
    # 处理动态配置选项
    use_dynamic_config = not args.disable_dynamic_config
    if use_dynamic_config:
        print("已启用动态配置功能，将实时监控配置文件变化")
        # 启动配置监控
        config_manager.start_monitoring()
        
        # 设置配置检查间隔
        if args.config_check_interval:
            print(f"设置配置检查间隔为 {args.config_check_interval} 秒")
            config_manager.check_interval = args.config_check_interval
        
        # 备份当前配置
        backup_file = config_manager.save_config_backup()
        if backup_file:
            print(f"配置文件已备份到 {backup_file}")
    else:
        print("已禁用动态配置功能，将使用静态配置")
        # 设置环境变量，告知策略不要启用动态配置
        os.environ['USE_DYNAMIC_CONFIG'] = '0'
    
    # 通用参数
    run_args = {
        'strategy_id': get_config_value('DEFAULT_STRATEGY_ID', '39da9282-3bbd-11f0-8755-d4e98a5e8c02'),
        'filename': 'main.py',
        'token': get_config_value('DEFAULT_TOKEN', '927022466b9f6476ef82fe30991f521c61feac74'),
        'mode': MODE_BACKTEST,
        'backtest_start_time': get_config_value('BACKTEST_START_TIME', '2023-01-01 09:30:00'),
        'backtest_end_time': get_config_value('BACKTEST_END_TIME', '2023-03-31 15:00:00'),
        'backtest_initial_cash': get_config_value('BACKTEST_INITIAL_CASH', 1000000),
        'backtest_adjust': ADJUST_NONE,
        'backtest_commission_ratio': get_config_value('BACKTEST_COMMISSION_RATIO', 0.0003),
        'backtest_slippage_ratio': get_config_value('BACKTEST_SLIPPAGE_RATIO', 0.0002)
    }
    
    # 如果平台传递了参数，则覆盖默认值
    if args.strategy_id:
        print(f"使用平台提供的策略ID: {args.strategy_id}")
        run_args['strategy_id'] = args.strategy_id
    if args.token:
        print(f"使用平台提供的令牌")
        run_args['token'] = args.token
    if args.mode is not None:
        print(f"使用平台提供的运行模式: {args.mode}")
        # 平台可能使用不同的模式值，需要映射
        mode_mapping = {
            0: MODE_BACKTEST,  # 回测模式
            1: MODE_LIVE,      # 实盘模式
            2: MODE_LIVE       # 模拟盘模式也使用实盘模式常量
        }
        if args.mode in mode_mapping:
            run_args['mode'] = mode_mapping[args.mode]
            print(f"映射为内部模式: {run_args['mode']} {'(实盘/模拟盘)' if args.mode in [1, 2] else '(回测)'}")
        else:
            run_args['mode'] = args.mode
            print(f"使用原始模式值: {args.mode}")
    
    # 记录未知参数（调试用）
    if unknown:
        print(f"接收到未知参数: {unknown}")
    
    # 运行前备份数据 - 只在回测模式下执行
    is_backtest = run_args.get('mode') == MODE_BACKTEST or args.mode == 0
    if is_backtest:
        try:
            from scripts.data_manager import backup_data
            print("开始备份数据...")
            backup_data()
            print("数据备份完成")
        except Exception as e:
            print(f"数据备份过程中出现错误: {str(e)}")
    else:
        print("在实盘/模拟盘模式下跳过数据备份")
    
    # 记录开始时间
    start_time = datetime.datetime.now()
    print(f"开始运行策略，时间: {start_time}")
    if use_dynamic_config:
        print(f"配置管理器已启动，实时监控配置文件变化，检查间隔: {config_manager.check_interval}秒")
    
    try:
        # 尝试运行策略
        run(**run_args)
    except Exception as e:
        print(f"策略运行过程中出现错误: {str(e)}")
        # 记录错误到日志文件
        with open("strategy_error.log", "a") as f:
            f.write(f"{datetime.datetime.now()} - 错误: {str(e)}\n")
    finally:
        # 停止配置监控
        if use_dynamic_config:
            print("停止配置监控...")
            config_manager.stop_monitoring()
    
    # 记录结束时间
    end_time = datetime.datetime.now()
    duration = end_time - start_time
    print(f"策略运行完成，时间: {end_time}")
    print(f"总耗时: {duration.total_seconds():.2f} 秒")

@profile
def buy_strategy(context):
    """
    买入策略实现

    修改说明:
    1. 优化了买入策略的执行逻辑
    2. 增加了详细的日志记录
    3. 增加了异常处理和性能优化
    4. 添加了未来函数检查和修复逻辑
    5. 集成了智能评分系统
    """
    try:
        # 记录买入策略开始时间
        start_time = time.time()

        # 检查是否在交易时间段或回测模式
        if not (is_trading_hour(context.now.time()) or context.run_mode == 'backtest'):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过买入策略执行")
            return

        # 检查买入信号是否启用
        if not get_config_value('ENABLE_BUY_SIGNALS', True):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入信号已禁用，跳过买入检查")
            return

        # 🚨 未来函数检查：确定当前时间是否可以使用当日成交量数据
        current_time = context.now.time()
        is_near_close = current_time >= datetime.time(14, 30)  # 14:30后可以使用当日数据

        # 设置数据完整性标志
        context.data_integrity_mode = True
        context.allow_intraday_volume = is_near_close

        if not is_near_close:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚨 未来函数保护：当前时间{current_time}，禁用当日成交量指标")
        else:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ 数据完整性检查：当前时间{current_time}，允许使用当日成交量指标")

        # 🎯 检查智能评分系统状态
        use_smart_scoring = getattr(context, 'smart_scoring_enabled', False) and hasattr(context, 'scoring_system')

        if use_smart_scoring:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 使用智能评分系统进行买入决策")
        else:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 使用传统策略进行买入决策")
            
        context.buy_check_count += 1
        
        # 显示买入检查开始信号
        log_info(context, f"开始第{context.buy_check_count}次买入检查")

        # 获取当前持仓
        positions = context.account().positions()
        holding_symbols = [p['symbol'] for p in positions] if positions else []

        # 显示当前持仓数量
        log_info(context, f"当前持仓数量: {len(holding_symbols)}")

        # 获取可用资金
        account = context.account()
        available_cash = account.cash.available
        total_assets = account.cash.nav  # 总资产价值

        log_info(context, f"可用资金: {available_cash:.2f}, 总资产: {total_assets:.2f}")

        if len(holding_symbols) >= context.max_positions:
            log_info(context, f"已达到最大持仓数量限制 {context.max_positions}")
            return

        if available_cash < 10000:  # 设置最小可用资金限制
            log_info(context, f"可用资金不足: {available_cash:.2f}")
            return
        
        # 获取股票池
        stock_pool = []
        
        # 检查是否有自定义订阅股票列表
        if hasattr(context, 'custom_symbols') and context.custom_symbols:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用自定义订阅股票列表作为股票池: {context.custom_symbols}")
            stock_pool = context.custom_symbols
            # 确保data_fetcher也有这个列表
            context.data_fetcher.constituents = context.custom_symbols
        # 检查data_fetcher的股票池
        elif hasattr(context.data_fetcher, 'constituents') and context.data_fetcher.constituents:
            stock_pool = context.data_fetcher.constituents
        # 如果股票池仍然为空，尝试轮换订阅
        else:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 股票池为空，尝试执行轮换订阅")
            
            # 检查是否在交易时间段
            if is_trading_hour(context.now.time()) or context.run_mode == 'backtest':
                # 尝试执行轮换订阅
                if hasattr(context.data_fetcher, 'rotate_subscription'):
                    rotation_result = context.data_fetcher.rotate_subscription(context.index_symbol)
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅结果: {'成功' if rotation_result else '失败'}")
                    
                    # 如果轮换订阅成功，获取股票池
                    if rotation_result and context.data_fetcher.constituents:
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅成功，继续执行买入策略")
                        stock_pool = context.data_fetcher.constituents
                    else:
                        context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅后股票池仍为空，等待下次买入检查")
                        return
                else:
                    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - data_fetcher没有rotate_subscription方法，无法执行轮换订阅")
                    return
            else:
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，无法执行轮换订阅")
                return
                
        # 初始化当日买入记录（如果还没有初始化）
        if not hasattr(context, 'today_bought_symbols'):
            context.today_bought_symbols = set()
            context.last_buy_date = context.now.date()

        # 检查是否需要重置当日买入记录
        current_date = context.now.date()
        if context.last_buy_date != current_date:
            if context.today_bought_symbols:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 日期变更，清空当日买入记录: {len(context.today_bought_symbols)}只股票")
            context.today_bought_symbols.clear()
            context.last_buy_date = current_date

        # 排除已持仓股票和当日已买入股票
        available_symbols = [s for s in stock_pool
                           if s not in holding_symbols and s not in context.today_bought_symbols]

        # 添加日志输出股票池信息
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 股票池筛选信息:")
        context.log.info(f"  📊 总股票池: {len(stock_pool)}只")
        context.log.info(f"  🏠 已持仓股票: {len(holding_symbols)}只")
        context.log.info(f"  📅 当日已买入: {len(context.today_bought_symbols)}只")
        context.log.info(f"  ✅ 可买入股票: {len(available_symbols)}只")

        # 检查是否有可用的股票
        if not available_symbols:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 没有可用的股票进行买入操作")
            if context.today_bought_symbols:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当日已买入股票: {list(context.today_bought_symbols)}")
            return
            
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 可选股票数 {len(available_symbols)}")

        # 🎯 智能评分策略：直接使用智能评分系统，不需要增强信号包装器
        enhanced_signals = None  # 禁用增强信号，直接使用智能评分系统
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 使用智能评分系统（直接调用模式）")
        
        # 随机选择一部分股票进行分析，以提高性能
        max_analysis_stocks = get_config_value('MAX_ANALYSIS_STOCKS', 100)
        if len(available_symbols) > max_analysis_stocks:
            import random
            # 随机选择指定数量的股票进行分析
            available_symbols = random.sample(available_symbols, max_analysis_stocks)
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 随机选择{max_analysis_stocks}只股票进行分析")
        
        # 使用增强的买入信号计算
        signal_start_time = time.time()
        signal_analysis_results = []

        # 初始化TRIX筛选变量（确保在所有代码路径中都被定义）
        trix_filtered_symbols = available_symbols

        # 🎯 智能评分策略：直接使用所有股票进行智能评分分析
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 使用智能评分系统分析，股票数: {len(available_symbols)}")

        # 直接使用所有可用股票，智能评分系统内部会进行筛选
        trix_filtered_symbols = available_symbols

        # 🚀 使用新的预筛选+智能评分策略
        signal_analysis_results = buy_strategy_main_logic(context, available_symbols)

        # 计算信号分析耗时
        signal_elapsed_time = time.time() - signal_start_time

        # 输出信号分析统计信息
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 信号分析完成:")
        context.log.info(f"  ⏱️ 分析耗时: {signal_elapsed_time:.2f}秒")
        context.log.info(f"  🎯 找到信号: {len(signal_analysis_results)}个买入信号")

        # 如果没有找到买入信号，直接返回
        if not signal_analysis_results:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 没有找到买入信号，结束买入策略")
            return

        # 🎯 使用原有的买入逻辑执行买入操作
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始执行买入操作，候选股票: {len(signal_analysis_results)}只")

        # 🚀 直接执行买入，不再重复检查（已完成筛选）
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 筛选完成，直接执行买入操作")

        # 直接使用备用买入逻辑（原生API + 数据存储）
        execute_backup_buy_logic(context, signal_analysis_results)

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入策略执行异常: {str(e)}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常详情: {traceback.format_exc()}")


def daily_trix_prefilter(context, symbols_list):
    """
    每日TRIX预筛选函数（带缓存机制）
    筛选条件：昨日TRIX < 前日TRIX（寻找可能反转的股票）

    参数:
    - context: 策略上下文
    - symbols_list: 股票代码列表

    返回:
    - list: 通过预筛选的股票代码列表
    """
    # 检查是否已有当日预筛选缓存
    today_str = context.now.strftime('%Y-%m-%d')
    cache_key = f'daily_prefilter_{today_str}'

    if hasattr(context, 'daily_prefilter_cache') and cache_key in context.daily_prefilter_cache:
        cached_result = context.daily_prefilter_cache[cache_key]
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 使用当日预筛选缓存，股票数: {len(cached_result)}")
        return cached_result

    # 🚀 获取日志配置（使用统一函数）
    current_log_config = get_log_config(context)

    # 根据模式决定是否显示开始日志
    if current_log_config.get('show_progress', True):
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 开始每日TRIX预筛选，股票总数: {len(symbols_list)}")

    # 🚀 性能优化：初始化TRIX缓存
    if not hasattr(context, 'trix_cache'):
        context.trix_cache = {}

    # 🚀 性能优化：检查是否可以复用昨日的TRIX计算
    yesterday_str = (context.now - pd.Timedelta(days=1)).strftime('%Y-%m-%d')
    trix_cache_key = f'trix_data_{today_str}'

    prefiltered_symbols = []
    start_time = time.time()

    # 🚀 性能优化：预计算常用值，避免在循环中重复计算
    cached_timestamp = get_cached_timestamp(context)
    current_log_config = get_log_config(context)  # 修复函数名
    max_debug_logs = current_log_config.get('max_debug_logs', 5)

    # 🚀 性能优化：预先获取配置值，避免在循环中重复调用
    trix_ema_period = get_config_value('TRIX_EMA_PERIOD', 3)
    use_talib_trix = get_config_value('USE_TALIB_TRIX', False)
    trix_threshold = get_config_value('TRIX_THRESHOLD', 0.0001)

    # 🚀 性能优化：预先导入numpy，避免在循环中重复导入
    import numpy as np

    # 统计变量
    data_success_count = 0
    data_fail_count = 0
    trix_success_count = 0
    trix_fail_count = 0
    condition_pass_count = 0
    condition_fail_count = 0

    # 🚀 尝试向量化TRIX计算
    vectorized_trix_results = {}
    if hasattr(context, 'advanced_optimizer') and context.advanced_optimizer:
        try:
            # 准备向量化计算的数据
            symbols_data = {}
            for symbol in symbols_list:
                data = all_data.get(symbol)
                if data is not None and len(data) >= 10:
                    close_prices = data['close'].values if hasattr(data, 'columns') else [item['close'] for item in data]
                    if len(close_prices) >= 10:
                        symbols_data[symbol] = close_prices

            # 向量化计算TRIX
            if symbols_data:
                vectorized_trix_results = context.advanced_optimizer.vectorized_trix_calculation(symbols_data)
                if vectorized_trix_results:
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 向量化TRIX计算完成: {len(vectorized_trix_results)}只股票")
        except Exception as e:
            context.log.debug(f"向量化TRIX计算失败: {e}")

    # 🚀 使用全局数据缓存管理器进行批量数据预加载
    all_data = {}
    if hasattr(context, 'global_data_cache') and context.global_data_cache:
        try:
            # 使用全局数据缓存管理器批量预加载数据
            all_data = context.global_data_cache.batch_preload_data(symbols_list, count=20, fields=['close'])
            if all_data:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 全局缓存批量预加载完成: {len(all_data)}只股票")
        except Exception as e:
            context.log.debug(f"全局缓存预加载失败: {e}")

    # 如果全局缓存不可用，使用高级优化器
    if not all_data and hasattr(context, 'advanced_optimizer') and context.advanced_optimizer:
        try:
            # 使用高级优化器批量预加载数据
            all_data = context.advanced_optimizer.batch_data_preload(symbols_list, count=20)
        except Exception as e:
            context.log.debug(f"高级优化器预加载失败: {e}")

    # 如果以上都不可用或失败，使用原有的批量获取逻辑
    if not all_data:
        perf_config = get_config_value('PERFORMANCE_OPTIMIZATION', {})
        batch_size = perf_config.get('batch_size', 100)

        if len(symbols_list) > batch_size:
            # 大批量时分批处理
            for batch_start in range(0, len(symbols_list), batch_size):
                batch_symbols = symbols_list[batch_start:batch_start + batch_size]
                try:
                    # 批量获取数据
                    batch_data = history_n(symbol=batch_symbols, count=20, frequency='1d', fields=['close'], end_time=context.now)
                    if isinstance(batch_data, dict):
                        all_data.update(batch_data)
                    else:
                        # 如果批量获取失败，回退到单个获取
                        for sym in batch_symbols:
                            all_data[sym] = history_n(symbol=sym, count=20, frequency='1d', fields=['close'], end_time=context.now)
                except:
                    # 批量失败时回退到单个获取
                    for sym in batch_symbols:
                        try:
                            all_data[sym] = history_n(symbol=sym, count=20, frequency='1d', fields=['close'], end_time=context.now)
                        except:
                            all_data[sym] = None
        else:
            # 小批量时直接批量获取
            try:
                all_data = history_n(symbol=symbols_list, count=20, frequency='1d', fields=['close'], end_time=context.now)
                if not isinstance(all_data, dict):
                    # 如果不是字典格式，回退到单个获取
                    all_data = {}
                    for symbol in symbols_list:
                        all_data[symbol] = history_n(symbol=symbol, count=20, frequency='1d', fields=['close'], end_time=context.now)
            except:
                # 批量获取失败，回退到单个获取
                all_data = {}
                for symbol in symbols_list:
                    try:
                        all_data[symbol] = history_n(symbol=symbol, count=20, frequency='1d', fields=['close'], end_time=context.now)
                    except:
                        all_data[symbol] = None

    for i, symbol in enumerate(symbols_list):
        try:
            # 从批量获取的数据中取出当前股票的数据
            data = all_data.get(symbol)

            if data is None or len(data) < 10:
                data_fail_count += 1
                if i < 5:  # 只显示前5个的详细信息
                    context.log.info(f"{cached_timestamp} - ❌ {symbol} 数据不足: data={'None' if data is None else f'长度{len(data)}'}")
                continue

            data_success_count += 1

            # 提取收盘价
            close_prices = data['close'].values if hasattr(data, 'columns') else [item['close'] for item in data]

            # 调试信息：检查数据格式（根据配置控制）
            max_debug_logs = current_log_config.get('max_individual_logs', 0)
            if (current_log_config.get('show_data_format_check', False) and
                i < max_debug_logs):
                context.log.info(f"{cached_timestamp} - 🔍 {symbol} 数据格式检查:")
                context.log.info(f"  数据类型: {type(data)}")
                context.log.info(f"  数据长度: {len(data)}")
                context.log.info(f"  收盘价长度: {len(close_prices)}")
                context.log.info(f"  收盘价类型: {type(close_prices)}")
                if len(close_prices) > 0:
                    context.log.info(f"  最后几个收盘价: {close_prices[-3:] if len(close_prices) >= 3 else close_prices}")

            # 🚀 优先使用向量化TRIX计算结果
            trix = None
            if symbol in vectorized_trix_results:
                trix = vectorized_trix_results[symbol]
                if (current_log_config.get('show_debug_details', False) and
                    i < max_debug_logs):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 {symbol} 使用向量化TRIX结果")
            else:
                # 🚀 性能优化：使用TRIX缓存管理器
                if hasattr(context, 'trix_cache_manager') and context.trix_cache_manager:
                    trix = context.trix_cache_manager.get_cached_trix(symbol)
                    if trix is not None:
                        if (current_log_config.get('show_debug_details', False) and
                            i < max_debug_logs):
                            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 使用TRIX缓存管理器")
                    else:
                        # 需要计算TRIX
                        import numpy as np
                        close_prices_array = np.array(close_prices, dtype=np.float64)

                        if len(close_prices_array) >= 10:
                            try:
                                # 使用统一的TRIX计算函数
                                trix = calculate_trix_unified(close_prices_array)
                                # 缓存TRIX结果到缓存管理器
                                context.trix_cache_manager.cache_trix(symbol, trix)
                            except Exception as trix_error:
                                trix = None
                else:
                    # 回退到原有的TRIX缓存逻辑
                    trix_symbol_key = f'{symbol}_{today_str}'
                    if trix_symbol_key in context.trix_cache:
                        trix = context.trix_cache[trix_symbol_key]
                        if (current_log_config.get('show_debug_details', False) and
                            i < max_debug_logs):
                            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 使用原有TRIX缓存")
                    else:
                        # 🔧 使用统一的TRIX计算函数（与买入信号检查保持一致）
                        import numpy as np

                        # 确保数据是numpy数组格式
                        close_prices_array = np.array(close_prices, dtype=np.float64)

                        if len(close_prices_array) >= 10:
                            try:
                                # 使用统一的TRIX计算函数，自动获取config中的周期配置
                                trix = calculate_trix_unified(close_prices_array)
                                # 缓存TRIX结果
                                context.trix_cache[trix_symbol_key] = trix

                                # TRIX计算详情日志（根据配置控制）
                                if (current_log_config.get('show_trix_calculation', False) and
                                    i < max_debug_logs):
                                    trix_period = get_config_value('TRIX_EMA_PERIOD', 3)
                                    use_talib_trix = get_config_value('USE_TALIB_TRIX', False)
                                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} TRIX计算结果:")
                                    context.log.info(f"  TRIX周期: {trix_period}, 使用talib: {use_talib_trix}")
                                    context.log.info(f"  TRIX类型: {type(trix)}")
                                    context.log.info(f"  TRIX长度: {len(trix) if trix is not None else 'None'}")
                                    if trix is not None and len(trix) > 0:
                                        context.log.info(f"  最后几个TRIX: {trix[-3:] if len(trix) >= 3 else trix}")
                            except Exception as trix_error:
                                trix = None
                                if (current_log_config.get('show_debug_details', False) and
                                    i < max_debug_logs):
                                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} TRIX计算异常: {trix_error}")
                        else:
                            trix = None

            if trix is not None and len(trix) >= 3:
                trix_success_count += 1
                # 预筛选条件：昨日TRIX < 前日TRIX（寻找下降趋势，为反转做准备）
                prev_trix = trix[-2]      # 昨日TRIX
                prev2_trix = trix[-3]     # 前日TRIX

                if prev_trix < prev2_trix:
                    condition_pass_count += 1
                    prefiltered_symbols.append(symbol)
                    # 显示通过预筛选的股票（根据配置控制）
                    if (current_log_config.get('show_individual_results', False) and
                        len(prefiltered_symbols) <= max_debug_logs):
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ {symbol} 通过预筛选: 昨日TRIX({prev_trix:.6f}) < 前日TRIX({prev2_trix:.6f})")
                else:
                    condition_fail_count += 1
                    # 显示未通过预筛选的股票（根据配置控制）
                    if (current_log_config.get('show_individual_results', False) and
                        i < max_debug_logs):
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 未通过预筛选: 昨日TRIX({prev_trix:.6f}) >= 前日TRIX({prev2_trix:.6f})")
            else:
                trix_fail_count += 1
                # 显示TRIX计算失败的股票（根据配置控制）
                if (current_log_config.get('show_debug_details', False) and
                    i < max_debug_logs):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} TRIX计算失败: trix={'None' if trix is None else f'长度{len(trix)}'}")

            # 🚀 性能优化：早期退出机制（可选）
            # 如果已经找到足够多的候选股票，可以提前退出
            max_prefilter_candidates = getattr(context, 'max_prefilter_candidates', None)
            if max_prefilter_candidates and len(prefiltered_symbols) >= max_prefilter_candidates:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 已找到足够候选股票({len(prefiltered_symbols)})，提前结束预筛选")
                break

            # 进度报告（根据配置控制间隔）
            progress_interval = current_log_config.get('progress_interval', 100)
            if (current_log_config.get('show_progress', True) and
                (i + 1) % progress_interval == 0):
                elapsed = time.time() - start_time
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 预筛选进度: {i+1}/{len(symbols_list)} ({(i+1)/len(symbols_list)*100:.1f}%), 已筛选出: {len(prefiltered_symbols)}只, 耗时: {elapsed:.2f}秒")

        except Exception as e:
            # 异常日志（根据配置控制）
            if (current_log_config.get('show_debug_details', False) and
                i < max_debug_logs):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 预筛选异常: {str(e)}")
            continue

    elapsed_time = time.time() - start_time
    filter_ratio = len(prefiltered_symbols) / len(symbols_list) * 100 if symbols_list else 0

    context.log.info(f"""
{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 每日TRIX预筛选完成:
  🔍 原始股票数: {len(symbols_list)}只
  ✅ 通过预筛选: {len(prefiltered_symbols)}只
  📉 筛选比例: {filter_ratio:.1f}%
  ⏱️ 筛选耗时: {elapsed_time:.2f}秒
  🚀 效率提升: 减少了{len(symbols_list) - len(prefiltered_symbols)}只股票的详细分析

  📊 详细统计:
  📈 数据获取成功: {data_success_count}只
  ❌ 数据获取失败: {data_fail_count}只
  📊 TRIX计算成功: {trix_success_count}只
  ❌ TRIX计算失败: {trix_fail_count}只
  ✅ 条件通过: {condition_pass_count}只
  ❌ 条件未通过: {condition_fail_count}只
""")

    # 缓存预筛选结果
    if not hasattr(context, 'daily_prefilter_cache'):
        context.daily_prefilter_cache = {}

    # 清理旧的缓存（保留最近3天）
    current_date = context.now.date()
    keys_to_remove = []
    for key in context.daily_prefilter_cache.keys():
        if key.startswith('daily_prefilter_'):
            cache_date_str = key.replace('daily_prefilter_', '')
            try:
                cache_date = datetime.datetime.strptime(cache_date_str, '%Y-%m-%d').date()
                if (current_date - cache_date).days > 3:
                    keys_to_remove.append(key)
            except:
                keys_to_remove.append(key)

    for key in keys_to_remove:
        del context.daily_prefilter_cache[key]

    # 保存当日预筛选结果
    context.daily_prefilter_cache[cache_key] = prefiltered_symbols
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 💾 预筛选结果已缓存，缓存键: {cache_key}")

    # 🚀 定期清理缓存和优化内存
    if hasattr(context, 'advanced_optimizer') and context.advanced_optimizer:
        try:
            # 每10次预筛选清理一次缓存
            if not hasattr(context, '_prefilter_count'):
                context._prefilter_count = 0
            context._prefilter_count += 1

            if context._prefilter_count % 10 == 0:
                context.advanced_optimizer.cleanup_cache()
                context.advanced_optimizer.optimize_memory_usage()

                # 显示性能统计
                if current_log_config.get('show_progress', True):
                    stats = context.advanced_optimizer.get_performance_stats()
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 性能统计: 缓存命中率{stats['cache_hit_rate']}, 缓存大小{stats['cache_size']}")
        except Exception as e:
            context.log.debug(f"缓存清理异常: {e}")

    return prefiltered_symbols


def daily_prefilter_task(context):
    """每日预筛选任务（在开盘时执行）"""
    try:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🌅 开始执行每日TRIX预筛选任务")

        # 获取可用股票列表
        available_symbols = get_available_symbols(context)
        if not available_symbols:
            context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 没有可用的股票进行预筛选")
            return

        # 执行预筛选（会自动缓存结果）
        prefiltered_symbols = daily_trix_prefilter(context, available_symbols)

        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 每日预筛选任务完成，预筛选股票数: {len(prefiltered_symbols)}")

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 每日预筛选任务异常: {str(e)}")


def analyze_single_symbol(context, symbol):
    """分析单个股票的智能评分信号"""
    try:
        # 获取当前股票在分析序列中的索引（用于日志控制）
        analysis_index = getattr(context, '_current_analysis_index', 0)

        # 根据日志配置决定是否显示详细日志
        if should_log(context, 'analysis_detail', analysis_index):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 开始analyze_single_symbol函数")

        # 🚀 优先使用全局数据缓存获取股票数据
        data = None
        if hasattr(context, 'global_data_cache') and context.global_data_cache:
            data = context.global_data_cache.get_cached_data(symbol, 60, '1d', ['open', 'high', 'low', 'close', 'volume'])
            if data is not None and should_log(context, 'data_format', analysis_index):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 使用全局缓存数据")

        # 如果缓存中没有数据，则直接获取
        if data is None:
            data = history_n(symbol=symbol, count=60, frequency='1d', fields=['open', 'high', 'low', 'close', 'volume'], end_time=context.now)
            # 缓存新获取的数据
            if data is not None and hasattr(context, 'global_data_cache') and context.global_data_cache:
                context.global_data_cache.cache_data(symbol, 60, '1d', ['open', 'high', 'low', 'close', 'volume'], data)

        if should_log(context, 'data_format', analysis_index):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 数据获取完成: data={'None' if data is None else f'长度{len(data)}'}")

        if data is None:
            if should_log(context, 'debug', analysis_index):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 数据为None，跳过")
            return None

        if len(data) < 30:
            if should_log(context, 'debug', analysis_index):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 数据不足30天({len(data)}天)，跳过")
            return None

        # 🔧 使用全局智能评分系统状态（避免重复检查）
        local_use_smart_scoring = getattr(context, 'smart_scoring_enabled_global', False)

        if should_log(context, 'analysis_detail', analysis_index):
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 使用全局智能评分状态: {local_use_smart_scoring}")

        # 检查数据格式和完整性
        if isinstance(data, list):
            # 如果是list格式，转换为DataFrame
            import pandas as pd
            if len(data) == 0:
                if should_log(context, 'debug', analysis_index):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 数据为空列表，跳过")
                return None

            # 假设data是包含字典的列表
            try:
                data = pd.DataFrame(data)
                if should_log(context, 'data_format', analysis_index):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 已转换list为DataFrame，形状: {data.shape}")
            except Exception as convert_error:
                if should_log(context, 'debug', analysis_index):
                    context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 数据转换失败: {convert_error}")
                return None

        # 检查必需的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if hasattr(data, 'columns'):
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                if should_log(context, 'debug', analysis_index):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 缺少列: {missing_columns}")
                return None

            # 检查数据是否包含NaN或无效值
            if data.isnull().any().any():
                # 尝试清理数据
                data = data.dropna()
                if len(data) < 30:
                    if should_log(context, 'debug', analysis_index):
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 清理后数据不足30天({len(data)}天)，跳过")
                    return None

        # 🚀 提取收盘价并处理实时价格替代
        try:
            close_prices = data['close'].values if hasattr(data, 'columns') else [item['close'] for item in data]
            if should_log(context, 'data_format', analysis_index):
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 提取收盘价成功，长度: {len(close_prices)}")
        except Exception as price_error:
            if should_log(context, 'debug', analysis_index):
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 提取收盘价失败: {price_error}")
            return None

        # 🎯 实时价格替代逻辑（与其他模块保持一致）
        use_realtime_price = get_config_value('USE_REALTIME_PRICE', True)
        if use_realtime_price and is_trading_hour(context.now.time()):
            try:
                # 获取当前价格
                current_data = current(symbols=symbol)
                if current_data and len(current_data) > 0:
                    current_price = current_data[0]['price']
                    # 替换最后一个收盘价为当前价格
                    if len(close_prices) > 0:
                        original_price = close_prices[-1]
                        close_prices = close_prices.copy()  # 避免修改原数据
                        close_prices[-1] = current_price
                        # 实时价格替代总是显示（重要信息）
                        if should_log(context, 'individual', analysis_index) or should_log(context, 'progress'):
                            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 使用实时价格 {current_price:.2f} 替代当日收盘价 {original_price:.2f}")
            except Exception as e:
                if should_log(context, 'debug', analysis_index):
                    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ {symbol} 获取实时价格异常: {str(e)}")

        # 🔧 计算TRIX指标（使用统一的TRIX计算函数）
        trix_period = get_config_value('TRIX_EMA_PERIOD', 3)  # 使用原来的TRIX周期配置
        use_talib_trix = get_config_value('USE_TALIB_TRIX', False)  # 使用原来的talib配置

        if len(close_prices) >= 30:
            # 使用统一的TRIX计算函数
            trix = calculate_trix_unified(close_prices, trix_period, use_talib_trix)

            if should_log(context, 'trix_calc', analysis_index):
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} TRIX计算成功，周期={trix_period}, 使用talib={use_talib_trix}, 长度: {len(trix)}")
        else:
            # 数据不足，使用零数组
            import numpy as np
            trix = np.array([0])
            if should_log(context, 'trix_calc', analysis_index):
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 数据不足30天，使用零TRIX，周期={trix_period}")

        # 🚀 TRIX反转信号检查：当日TRIX > 昨日TRIX（昨日<前日已在预筛选中确认）
        if should_log(context, 'analysis_detail', analysis_index):
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 开始TRIX反转信号检查")

        trix_reversal_signal = False
        if len(trix) >= 2:
            current_trix = trix[-1]
            prev_trix = trix[-2]

            # 只需检查当日TRIX > 昨日TRIX（反转向上）
            trix_reversal_signal = current_trix > prev_trix

            if trix_reversal_signal:
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ {symbol} TRIX反转信号确认: 当日{current_trix:.6f} > 昨日{prev_trix:.6f}")
            else:
                context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} TRIX反转信号未确认: 当日{current_trix:.6f} <= 昨日{prev_trix:.6f}")
                return None  # 没有反转信号，直接返回
        else:
            context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} TRIX数据不足，跳过")
            return None

        # 🎯 智能评分系统决策逻辑（仅对确认反转的股票）
        if local_use_smart_scoring:
            # 智能评分系统启用时的处理
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} TRIX反转信号确认，开始智能评分分析")

            try:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 开始智能评分分析...")

                # 准备评分系统所需的数据
                scoring_data = prepare_scoring_data(symbol, data, context)

                # 获取买入信号和评分
                buy_signal, score, score_details = context.scoring_system.get_buy_signal(scoring_data)

                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 {symbol} 智能评分结果: 评分={score:.1f}, 买入信号={buy_signal}")

                if buy_signal:
                    # 获取当前价格
                    try:
                        if hasattr(data, 'columns'):
                            current_price = data['close'].iloc[-1]
                        else:
                            current_price = data[-1]['close']
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 当前价格: {current_price}")
                    except Exception as price_error:
                        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 获取当前价格失败: {price_error}")
                        return None

                    # 根据评分确定仓位大小
                    position_size = context.scoring_system.get_position_size(score)

                    return {
                        'symbol': symbol,
                        'current_price': current_price,
                        'smart_score': score,
                        'score_details': score_details,
                        'position_size': position_size,
                        'buy_signal_type': 'trix_smart_scoring',  # 表示通过TRIX+智能评分
                        'final_buy_signal': True,
                        'timestamp': context.now,
                        'trix_current': trix[-1] if len(trix) > 0 else 0,
                        'trix_prev': trix[-2] if len(trix) > 1 else 0,
                        'trix_prev2': trix[-3] if len(trix) > 2 else 0,
                        'trix_signal_passed': True,
                        'trix_reversal_confirmed': True,
                        'signal_reason': f"TRIX反转+智能评分: 预筛选通过→反转确认→智能评分{score:.1f}分"
                    }
                return None

            except Exception as scoring_error:
                context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 智能评分系统失败: {scoring_error}")
                # 回退到传统策略
                pass

        # 🔧 智能评分系统未启用，直接使用基础TRIX信号（无需额外日志）

        # 获取当前价格
        try:
            if hasattr(data, 'columns'):
                current_price = data['close'].iloc[-1]
            else:
                current_price = data[-1]['close']
        except Exception as price_error:
            context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 获取价格失败: {price_error}")
            return None

        return {
            'symbol': symbol,
            'current_price': current_price,
            'trix_current': trix[-1] if len(trix) > 0 else 0,
            'trix_prev': trix[-2] if len(trix) > 1 else 0,
            'trix_prev2': trix[-3] if len(trix) > 2 else 0,
            'buy_signal_type': 'basic_trix',
            'final_buy_signal': True,
            'timestamp': context.now,
            'trix_signal_passed': True,
            'trix_reversal_confirmed': True,
            'signal_reason': f"基础TRIX反转: 预筛选通过→反转确认({trix[-1]:.6f} > {trix[-2]:.6f})"
        }

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} analyze_single_symbol异常: {e}")
        import traceback
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 异常详情: {traceback.format_exc()}")
        return None


def buy_strategy_main_logic(context, available_symbols):
    """买入策略主逻辑"""
    # 🚀 每日TRIX预筛选：只分析昨日TRIX < 前日TRIX的股票
    daily_prefiltered_symbols = daily_trix_prefilter(context, available_symbols)

    # 检查预筛选结果，如果太少则使用备选策略
    min_prefilter_count = max(5, len(available_symbols) // 50)  # 至少5只，或总数的2%

    if len(daily_prefiltered_symbols) == 0:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 每日预筛选未找到符合条件的股票，使用原始信号生成器")
        signal_analysis_results = context.signal_generator.analyze_signals(available_symbols)
    elif len(daily_prefiltered_symbols) < min_prefilter_count:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ 预筛选股票数({len(daily_prefiltered_symbols)})过少，补充使用前{min_prefilter_count}只股票进行分析")
        # 使用预筛选结果 + 部分原始股票
        supplementary_symbols = available_symbols[:min_prefilter_count]
        combined_symbols = list(set(daily_prefiltered_symbols + supplementary_symbols))
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 合并后分析股票数: {len(combined_symbols)}")
        daily_prefiltered_symbols = combined_symbols
    else:
        # 🎯 对预筛选股票进行TRIX反转+智能评分分析
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 开始TRIX反转+智能评分分析，预筛选股票数: {len(daily_prefiltered_symbols)}")

        trix_reversal_count = 0
        smart_scoring_count = 0
        signal_analysis_results = []

        for i, symbol in enumerate(daily_prefiltered_symbols):
            try:
                # 设置当前分析索引，用于日志控制
                context._current_analysis_index = i

                # 根据日志配置决定是否显示分析开始日志
                if should_log(context, 'individual', i):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 开始分析 {symbol}")
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 准备调用analyze_single_symbol({symbol})")

                result = analyze_single_symbol(context, symbol)

                if should_log(context, 'analysis_detail', i):
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 analyze_single_symbol返回: {type(result)}")

                if result is not None:
                    signal_analysis_results.append(result)
                    if result.get('trix_reversal_confirmed'):
                        trix_reversal_count += 1
                    if result.get('buy_signal_type') in ['trix_smart_scoring', 'smart_scoring']:
                        smart_scoring_count += 1

                    # 成功信号总是显示（重要信息）
                    if should_log(context, 'individual', i) or should_log(context, 'progress'):
                        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ {symbol} 信号成功: {result.get('buy_signal_type', 'unknown')}")
                else:
                    if should_log(context, 'debug', i):
                        context.log.debug(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 信号失败")
            except Exception as outer_e:
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 外层调用异常: {outer_e}")
                import traceback
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 外层异常详情: {traceback.format_exc()}")

            # 注释掉效率优化限制，分析所有符合条件的股票
            # if len(signal_analysis_results) >= 10:  # 找到10个买入信号就够了
            #     context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🚀 效率优化：已找到{len(signal_analysis_results)}个买入信号，停止分析")
            #     break

        # 📊 输出每日预筛选和智能评分统计信息
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 每日预筛选+TRIX反转+智能评分分析完成:")
        context.log.info(f"  🔍 每日预筛选: {len(available_symbols)} → {len(daily_prefiltered_symbols)}只")
        context.log.info(f"  🚀 TRIX反转确认: {trix_reversal_count}只")
        context.log.info(f"  🎯 智能评分买入: {smart_scoring_count}只")
        context.log.info(f"  ✅ 总买入信号: {len(signal_analysis_results)}只")

        # 🔧 如果没有找到结果，使用原始信号生成器作为备选
        if len(signal_analysis_results) == 0:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 预筛选+反转+智能评分未找到结果，使用原始信号生成器")
            signal_analysis_results = context.signal_generator.analyze_signals(available_symbols)

    return signal_analysis_results


def load_custom_subscribe_symbols(context):
    """加载自定义订阅股票列表"""
    try:
        # 初始化自定义订阅股票列表
        custom_subscribe_mode = get_config_value('CUSTOM_SUBSCRIBE_MODE', False)
        custom_subscribe_symbols = get_config_value('CUSTOM_SUBSCRIBE_SYMBOLS', [])

        if custom_subscribe_mode and custom_subscribe_symbols:
            context.custom_symbols = custom_subscribe_symbols
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已从配置文件加载自定义订阅股票列表: {context.custom_symbols}")
        else:
            context.custom_symbols = None
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 未启用自定义订阅模式，将使用默认订阅逻辑")
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 加载自定义订阅股票列表异常: {str(e)}")
        context.custom_symbols = None


def should_buy_with_enhanced_factors(context, symbol, enhanced_factors):
    """基于增强因子决定是否买入（可配置筛选模式）"""
    try:
        if not ENHANCED_FACTORS_AVAILABLE:
            return True  # 如果增强因子不可用，默认通过

        # 检测使用的因子系统类型
        factor_count = len(enhanced_factors)
        is_complete_system = factor_count > 100  # 192因子系统

        # 获取因子信息
        overall_score = enhanced_factors.get('enhanced_overall_score', 50)
        confidence_level = enhanced_factors.get('enhanced_confidence_level', 50)

        # 根据因子系统类型设置不同的阈值
        if is_complete_system:
            # 192因子系统：更严格的标准
            score_threshold = get_config_value('ENHANCED_FACTORS_SCORE_THRESHOLD', 60)
            confidence_threshold = get_config_value('ENHANCED_FACTORS_CONFIDENCE_THRESHOLD', 60)
        else:
            # 原有系统：相对宽松的标准
            score_threshold = get_config_value('ENHANCED_FACTORS_SCORE_THRESHOLD', 50)
            confidence_threshold = get_config_value('ENHANCED_FACTORS_CONFIDENCE_THRESHOLD', 50)

        # 判断是否通过增强因子筛选
        should_buy = overall_score >= score_threshold and confidence_level >= confidence_threshold

        return should_buy

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 增强因子买入判断异常: {str(e)}")
        return True  # 异常时默认通过


def get_log_config(context):
    """获取当前日志配置"""
    try:
        from config import get_config_value
        logging_config = get_config_value('LOGGING_CONFIG', {})
        logging_presets = get_config_value('LOGGING_PRESETS', {})

        # 应用预设模式（数字控制）
        log_mode = logging_config.get('mode', 1)  # 默认极简模式
        if log_mode in logging_presets:
            current_log_config = logging_presets[log_mode].copy()
            current_log_config.update(logging_config)  # 用户自定义覆盖预设
        else:
            # 如果模式无效，使用极简模式
            current_log_config = logging_presets[1].copy()
            current_log_config.update(logging_config)

        return current_log_config
    except:
        # 如果获取配置失败，返回极简模式
        return {
            'show_progress': True,
            'show_debug_details': False,
            'show_data_format_check': False,
            'show_trix_calculation': False,
            'show_individual_results': False,
            'progress_interval': 100,
            'max_individual_logs': 0,
        }

def should_log(context, log_type, index=0):
    """判断是否应该记录日志"""
    config = get_log_config(context)

    if log_type == 'progress':
        return config.get('show_progress', True)
    elif log_type == 'debug':
        return config.get('show_debug_details', False)
    elif log_type == 'data_format':
        return config.get('show_data_format_check', False) and index < config.get('max_individual_logs', 0)
    elif log_type == 'trix_calc':
        return config.get('show_trix_calculation', False) and index < config.get('max_individual_logs', 0)
    elif log_type == 'individual':
        return config.get('show_individual_results', False) and index < config.get('max_individual_logs', 0)
    elif log_type == 'analysis_detail':
        return config.get('show_debug_details', False) and index < config.get('max_individual_logs', 0)

    return False

def calculate_trix_unified(close_prices, period=None, use_talib=None):
    """
    统一的TRIX计算函数，与signal_generator保持一致

    参数:
    - close_prices: 收盘价数组
    - period: TRIX的EMA周期，默认从config获取
    - use_talib: 是否使用talib直接计算，默认从config获取

    返回:
    - trix: TRIX指标数组
    """
    try:
        # 获取配置参数
        if period is None:
            period = get_config_value('TRIX_EMA_PERIOD', 3)
        if use_talib is None:
            use_talib = get_config_value('USE_TALIB_TRIX', False)

        import talib
        import numpy as np

        if use_talib:
            # 使用talib直接计算TRIX
            trix = talib.TRIX(close_prices, timeperiod=period)
        else:
            # 使用自定义计算方法（与signal_generator保持一致）
            ema1 = talib.EMA(close_prices, timeperiod=period)
            ema2 = talib.EMA(ema1, timeperiod=period)
            ema3 = talib.EMA(ema2, timeperiod=period)

            # 计算TRIX线
            trix = np.zeros_like(close_prices)
            for i in range(1, len(ema3)):
                if ema3[i-1] != 0:  # 避免除以零
                    trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100

        return trix
    except Exception as e:
        # 返回零数组作为fallback
        import numpy as np
        return np.array([0])

def execute_backup_buy_logic(context, signal_analysis_results):
    """备用买入逻辑，使用简化的买入执行"""
    try:
        # 获取当前持仓信息
        positions = context.account().positions()
        holding_symbols = [pos.symbol for pos in positions if pos.volume > 0]
        available_cash = context.account().cash.available

        # 计算可买入数量
        max_new_positions = context.max_positions - len(holding_symbols)
        max_buy_stocks = min(max_new_positions, len(signal_analysis_results))

        if max_buy_stocks <= 0:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已达到最大持仓数量，无法买入新股票")
            return

        # 按评分排序
        sorted_candidates = sorted(signal_analysis_results,
                                 key=lambda x: x.get('smart_score', x.get('trix_current', 0)),
                                 reverse=True)

        # 初始化当日买入记录（如果还没有初始化）
        if not hasattr(context, 'today_bought_symbols'):
            context.today_bought_symbols = set()
            context.last_buy_date = context.now.date()

        buy_count = 0
        for i in range(min(max_buy_stocks, len(sorted_candidates))):
            candidate = sorted_candidates[i]
            symbol = candidate['symbol']
            price = candidate['current_price']

            # 🔒 执行前再次检查：确保不重复买入已持仓或当日已买入的股票
            if symbol in holding_symbols:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ {symbol} 已持仓，跳过买入")
                continue

            if symbol in context.today_bought_symbols:
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ {symbol} 当日已买入，跳过重复买入")
                continue

            # 简化的资金分配：平均分配
            cash_per_stock = available_cash / (max_new_positions - buy_count) if (max_new_positions - buy_count) > 0 else available_cash
            volume = int(cash_per_stock / price / 100) * 100  # 取整百

            if volume < 100:
                continue

            if volume * price > available_cash:
                break

            # 🔄 使用原来的掘金API买入
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 原生API买入 {symbol}: 价格{price:.2f}, 数量{volume}")

            try:
                # 使用原来的order_volume API
                order_result = order_volume(
                    symbol=symbol,
                    volume=volume,
                    side=OrderSide_Buy,
                    order_type=OrderType_Market,
                    position_effect=PositionEffect_Open
                )

                if order_result:
                    # 🔒 买入成功后立即记录到当日买入集合
                    context.today_bought_symbols.add(symbol)
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📝 {symbol} 已添加到当日买入记录")

                    # 🚀 保存买入记录到数据库（使用原来的数据存储方式）
                    save_original_buy_record(context, symbol, price, volume, candidate)

                    available_cash -= volume * price
                    buy_count += 1
                    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ✅ {symbol} 原生API买入成功")
                else:
                    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ {symbol} 原生API买入失败")

            except Exception as buy_error:
                context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 买入异常: {str(buy_error)}")
                continue

        # 📊 买入完成统计
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🎯 买入执行完成:")
        context.log.info(f"  📈 候选股票: {len(signal_analysis_results)}只")
        context.log.info(f"  ✅ 成功买入: {buy_count}只")
        context.log.info(f"  💰 剩余资金: {available_cash:.2f}元")
        context.log.info(f"  📊 当前持仓: {len(holding_symbols) + buy_count}只")

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 备用买入逻辑异常: {str(e)}")

def save_original_buy_record(context, symbol, price, volume, signal_data):
    """保存买入记录到数据库（使用原来的数据存储方式）"""
    try:
        # 构建买入记录数据（与原来的signal_generator格式兼容）
        buy_record = {
            'Timestamp': context.now.strftime('%Y-%m-%d %H:%M:%S'),
            'Symbol': symbol,
            'Action': 'BUY',
            'Current_Price': float(price),
            'Volume': int(volume),
            'Final_Buy_Signal': 1,
            'Signal_Type': signal_data.get('buy_signal_type', 'trix_reversal'),
        }

        # 添加TRIX相关数据
        if 'trix_current' in signal_data:
            buy_record.update({
                'TRIX_Current': signal_data.get('trix_current', 0),
                'TRIX_Prev': signal_data.get('trix_prev', 0),
                'TRIX_Prev2': signal_data.get('trix_prev2', 0),
                'TRIX_Reversal_Signal': int(signal_data.get('trix_reversal_confirmed', False)),
            })

        # 添加智能评分数据（如果有）
        if 'smart_score' in signal_data:
            buy_record.update({
                'Smart_Score': signal_data.get('smart_score', 0),
                'Position_Size': signal_data.get('position_size', 1.0),
            })

        # 添加基础技术指标（简化版，与原来格式兼容）
        buy_record.update({
            'MA3': 0,  # 可以后续计算
            'MA7': 0,
            'MA20': 0,
            'RSI': 0,
            'MACD': 0,
            'Volume_Ratio': 0,
            'Trend_Strength': 0,
            'Signal_Reason': signal_data.get('signal_reason', 'TRIX反转信号'),
        })

        # 🚀 优先使用批量数据库管理器保存
        if hasattr(context, 'batch_db_manager') and context.batch_db_manager:
            context.batch_db_manager.add_record(buy_record)
            context.log.info(f"{get_cached_timestamp(context)} - 💾 {symbol} 买入记录已添加到批量队列")
        elif hasattr(context, 'data_manager') and context.data_manager:
            # 使用数据管理器保存
            context.data_manager.save_trade(buy_record)
            context.log.info(f"{get_cached_timestamp(context)} - 💾 {symbol} 买入记录已保存到数据库")
        else:
            # 直接保存到数据库
            save_analysis(buy_record)
            context.log.info(f"{get_cached_timestamp(context)} - 💾 {symbol} 买入记录已直接保存到数据库")

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 保存买入记录异常: {str(e)}")
