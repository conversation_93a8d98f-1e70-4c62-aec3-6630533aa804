# coding=utf-8
from gm.api import *
try:
    from gm.model import DictLikeOrder
except ImportError:
    DictLikeOrder = type('DictLikeOrder', (dict,), {})
import datetime
import numpy as np
import time

class TradeExecutor:
    def __init__(self, context):
        self.context = context
        self.initial_cash = getattr(context, 'initial_cash', 1000000)
        self.max_retry_count = 3
        self.retry_interval = 1  # 重试间隔（秒）
        self.slippage_rate = 0.0002  # 滑点率
        self.commission_rate = 0.0003  # 手续费率
        self.max_holdings = getattr(context, 'max_positions', 95)  # 使用context中的参数，默认值为95
        
        # 记录初始化时的持仓比例值，仅用于日志
        current_position_ratio = getattr(context, 'single_position_ratio', 0.15)
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - TradeExecutor初始化时的单只股票最大持仓比例: {current_position_ratio*100:.2f}%")
        
    def execute_order(self, symbol, volume, price, side, position_effect):
        """执行订单"""
        try:
            if not hasattr(self.context, 'positions'):
                self.context.positions = {}
                
            current_holding = self.context.positions.get(symbol, 0)
            
            # 每次执行订单时都从context获取最新的持仓比例值
            current_position_ratio = getattr(self.context, 'single_position_ratio', 0.15)
            min_position_ratio = getattr(self.context, 'min_position_ratio', 0.03)
            
            # 获取当前总资产和可用资金
            account = self.context.account()
            total_assets = account.cash.nav  # 总资产价值
            available_cash = account.cash.available  # 可用资金
            
            # 计算目标持仓金额 - 基于当前总资产而不是初始资金
            ideal_target_value = total_assets * current_position_ratio
            min_target_value = total_assets * min_position_ratio
            
            # 动态调整目标持仓金额，考虑可用资金状况
            if side == OrderSide_Buy:
                # 计算买入成本（包括手续费和滑点）
                estimated_cost = price * volume * (1 + self.commission_rate + self.slippage_rate)
                
                # 检查可用资金是否足够理想持仓金额
                if available_cash >= estimated_cost:
                    # 可用资金充足，使用理想目标持仓金额
                    target_position_value = ideal_target_value
                    adjusted_reason = "理想目标"
                elif available_cash >= min_target_value:
                    # 可用资金不足理想目标但超过最小目标，动态调整
                    # 使用可用资金的90%作为目标持仓金额，留出10%作为缓冲
                    target_position_value = available_cash * 0.9
                    adjusted_reason = "动态调整"
                else:
                    # 可用资金不足最小持仓金额，使用可用资金（如果大于100股价值）
                    min_shares_value = price * 100  # 最小100股的价值
                    if available_cash >= min_shares_value * 1.01:  # 加1%缓冲
                        target_position_value = available_cash * 0.99  # 使用99%的可用资金
                        adjusted_reason = "最小持仓"
                    else:
                        # 资金不足买入最小单位(100股)
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 可用资金({available_cash:.2f})不足以买入最小交易单位")
                        return None
            else:
                # 卖出操作不需要调整目标持仓金额
                target_position_value = ideal_target_value
                adjusted_reason = "卖出操作"
            
            # 计算最大可买入数量
            max_volume = int(target_position_value / price)
            
            # 记录当前使用的持仓比例和调整情况
            actual_ratio = target_position_value / total_assets
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 持仓比例: 理想={current_position_ratio*100:.2f}%, 实际={actual_ratio*100:.2f}% ({adjusted_reason}), 总资产: {total_assets:.2f}, 可用资金: {available_cash:.2f}, 目标金额: {target_position_value:.2f}")
            
            if side == OrderSide_Buy:
                # 检查是否超过最大持股数限制
                if len(self.context.positions) >= self.max_holdings and symbol not in self.context.positions:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已达到最大持股数限制({self.max_holdings}只)")
                    return None
                    
                # 调整买入数量以符合单只股票仓位限制
                if current_holding + volume > max_volume:
                    original_volume = volume
                    volume = max_volume - current_holding
                    if volume <= 0:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 已达到单只股票最大仓位限制(实际比例:{actual_ratio*100:.2f}%)")
                        return None
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 调整买入数量: {original_volume}→{volume}股以符合仓位限制({actual_ratio*100:.2f}%)")
                
                # 确保买入数量是100的整数倍
                if volume % 100 != 0:
                    volume = (volume // 100) * 100
                    if volume <= 0:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 调整后买入数量不足100股，无法买入")
                        return None
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 调整买入数量为{volume}股(取整百)")
            
            # 智能价格计算
            adjusted_price = self._calculate_smart_price(symbol, price, side)
            if adjusted_price is None:
                adjusted_price = price
                
            # 检查交易时间
            if not self._validate_trading_time():
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间内")
                return None
                
            # 资金检查 - 只在买入时检查
            if side == OrderSide_Buy:
                total_cost = self._calculate_total_cost(volume, adjusted_price)
                if not self._check_available_cash(total_cost):
                    return None
                
            # 执行订单
            result = None
            last_error = None
            for attempt in range(self.max_retry_count):
                try:
                    result = self._submit_order(symbol, volume, adjusted_price, side, position_effect)
                    if result:
                        # 立即更新持仓信息
                        if side == OrderSide_Buy:
                            self.context.positions[symbol] = current_holding + volume
                            # 更新成本信息
                            cost_info = self.update_cost_info(symbol, adjusted_price, volume)
                            # 🚀 保存买入记录到数据库
                            self._save_buy_record(symbol, adjusted_price, volume, result)
                            # 🔒 更新当日买入记录
                            if hasattr(self.context, 'today_bought_symbols'):
                                self.context.today_bought_symbols.add(symbol)
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📝 {symbol} 已添加到当日买入记录(TradeExecutor)")
                        else:
                            new_holding = current_holding - volume
                            if new_holding <= 0:
                                if symbol in self.context.positions:
                                    del self.context.positions[symbol]
                                if symbol in self.context.positions_cost:
                                    del self.context.positions_cost[symbol]
                            else:
                                self.context.positions[symbol] = new_holding
                                
                        # 记录交易统计
                        position_info = self._update_position_info(symbol, volume, result)
                        self._log_trade_stats(symbol, result, position_info)
                        return result
                        
                except Exception as e:
                    last_error = str(e)
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 第{attempt + 1}次下单失败: {str(e)}")
                    if attempt < self.max_retry_count - 1:
                        time.sleep(self.retry_interval)
                        if side == OrderSide_Buy:
                            adjusted_price *= 1.001
                        else:
                            adjusted_price *= 0.999
                            
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 达到最大重试次数，下单失败: {last_error}")
            return None
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 执行订单异常: {str(e)}")
            return None
            
    def buy(self, symbol, price, volume):
        """买入股票的便捷方法

        参数:
        - symbol: 股票代码
        - price: 买入价格
        - volume: 买入数量

        返回:
        - bool: 买入是否成功
        """
        try:
            # 🔒 检查是否已持仓或当日已买入
            if hasattr(self.context, 'positions') and symbol in self.context.positions:
                if self.context.positions[symbol] > 0:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 已持仓，跳过买入")
                    return False

            if hasattr(self.context, 'today_bought_symbols') and symbol in self.context.today_bought_symbols:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 当日已买入，跳过重复买入")
                return False

            # 调用execute_order执行买入操作
            result = self.execute_order(
                symbol=symbol,
                volume=volume,
                price=price,
                side=OrderSide_Buy,
                position_effect=PositionEffect_Open
            )
            return result is not None
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 买入操作异常: {str(e)}")
            return False
            
    def _calculate_smart_price(self, symbol, base_price, side):
        """计算智能委托价格"""
        try:
            # 获取价格调整比例
            price_adjust_ratio = self._get_config_value('PRICE_ADJUST_RATIO', 0.002)
            
            # 正确使用current API获取实时价格
            quote = current([symbol])
            if not quote or len(quote) == 0:
                return base_price
                
            symbol_quote = quote[0]
            current_price = symbol_quote['price'] if 'price' in symbol_quote else base_price
            bid_price = symbol_quote['bid_price'] if 'bid_price' in symbol_quote else current_price
            ask_price = symbol_quote['ask_price'] if 'ask_price' in symbol_quote else current_price
            
            if side == OrderSide_Buy:
                # 买入价略高于当前价
                smart_price = min(
                    ask_price * (1 + price_adjust_ratio/2) if ask_price else current_price * (1 + price_adjust_ratio/2),
                    base_price * (1 + price_adjust_ratio)  # 最多高于基准价0.2%（或配置的比例）
                )
            else:
                # 卖出价略低于当前价
                smart_price = max(
                    bid_price * (1 - price_adjust_ratio/2) if bid_price else current_price * (1 - price_adjust_ratio/2),
                    base_price * (1 - price_adjust_ratio)  # 最多低于基准价0.2%（或配置的比例）
                )
                
            return smart_price
            
        except Exception as e:
            self.context.log.error(f"计算智能价格失败: {str(e)}")
            return base_price
            
    def _validate_trading_time(self):
        """验证当前是否在交易时间内"""
        now = self.context.now.time()
        
        # 上午交易时段: 9:30 - 11:30
        morning_start = datetime.time(9, 30)
        morning_end = datetime.time(11, 30)
        
        # 下午交易时段: 13:00 - 15:00
        afternoon_start = datetime.time(13, 0)
        afternoon_end = datetime.time(15, 0)
        
        # 检查是否在交易时间内
        is_trading_time = (morning_start <= now <= morning_end) or (afternoon_start <= now <= afternoon_end)
        
        # 检查是否为回测模式
        is_backtest = False
        
        # 方法1：检查context.run_mode
        if hasattr(self.context, 'run_mode') and self.context.run_mode == 'backtest':
            is_backtest = True
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到run_mode为backtest")
        
        # 方法2：检查data_fetcher.is_backtest
        elif hasattr(self.context, 'data_fetcher') and hasattr(self.context.data_fetcher, 'is_backtest'):
            is_backtest = self.context.data_fetcher.is_backtest
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 从data_fetcher.is_backtest获取回测状态: {is_backtest}")
        
        # 方法3：尝试直接判断是否为回测环境
        else:
            try:
                # 假设回测环境下可以订阅大量股票
                test_symbols = ['SHSE.600000'] * 10
                subscribe(symbols=test_symbols, frequency='60s', count=1)
                unsubscribe(symbols=test_symbols)
                is_backtest = True
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 通过订阅测试判断为回测模式")
            except Exception as e:
                if "订阅代码数量超过用户权限" in str(e):
                    is_backtest = False
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 通过订阅测试判断为实盘/模拟盘模式")
                else:
                    # 如果出现其他异常，默认认为是回测模式
                    is_backtest = True
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅测试异常，默认使用回测模式: {str(e)}")
        
        # 为了安全起见，在回测模式下设置context.run_mode
        if is_backtest and not hasattr(self.context, 'run_mode'):
            self.context.run_mode = 'backtest'
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置context.run_mode为backtest")
        
        # 添加详细日志
        if not is_trading_time:
            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前时间 {now} 不在交易时间内 (上午: {morning_start}-{morning_end}, 下午: {afternoon_start}-{afternoon_end})")
        else:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前时间 {now} 在交易时间内")
        
        # 在回测模式下忽略交易时间限制
        if is_backtest:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式，忽略交易时间限制")
            return True
        
        # 在实盘/模拟盘模式下，如果不在交易时间内，添加更详细的日志
        if not is_trading_time:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 交易时间验证失败，当前时间 {now} 不在交易时间内")
        
        return is_trading_time
        
    def _calculate_total_cost(self, volume, price):
        """计算交易总成本（含手续费和滑点）"""
        base_cost = volume * price
        commission = base_cost * self.commission_rate
        slippage = base_cost * self.slippage_rate
        
        return base_cost + commission + slippage
        
    def _check_available_cash(self, total_cost):
        """检查可用资金是否足够"""
        account = self.context.account()
        available_cash = account.cash.available
        
        if available_cash < total_cost:
            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 可用资金不足: 需要 {total_cost:.2f}, 可用 {available_cash:.2f}")
            return False
            
        return True
            
    def _submit_order(self, symbol, volume, price, side, position_effect):
        """提交订单"""
        try:
            # 获取订单类型配置
            use_market_order = self._get_config_value('USE_MARKET_ORDER', False)
            
            # 根据配置选择订单类型
            order_type = OrderType_Market if use_market_order else OrderType_Limit
            
            # 记录订单类型
            order_type_str = "市价单" if use_market_order else "限价单"
            side_str = "买入" if side == OrderSide_Buy else "卖出"
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 准备使用{order_type_str}{side_str}: 价格{price:.3f}, 数量{volume}")
            
            # 检查交易时间
            is_trading_time = self._validate_trading_time()
            if not is_trading_time:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单提交失败: 当前不在交易时间内")
                return None
            
            # 提交订单
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 正在提交{side_str}订单: {symbol}, 数量{volume}, 价格{price:.3f}")
            
            # 记录订单参数
            order_params = {
                'symbol': symbol,
                'volume': volume,
                'side': side,
                'order_type': order_type,
                'price': price,
                'position_effect': position_effect
            }
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订单参数: {order_params}")
            
            # 执行订单
            result = order_volume(
                symbol=symbol,
                volume=volume,
                side=side,
                order_type=order_type,
                price=price,  # 市价单时此参数会被忽略
                position_effect=position_effect
            )
            
            # 详细记录订单结果
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单提交结果类型: {type(result)}")
            
            # 改进订单返回结果处理
            if isinstance(result, list):
                # 如果返回列表，取第一个订单
                if len(result) > 0:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单返回列表，长度: {len(result)}")
                    order = result[0]
                    if isinstance(order, dict):
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单结果是字典: {order}")
                        return order
                    elif hasattr(order, '__dict__'):
                        order_dict = vars(order)
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单结果是对象: {order_dict}")
                        return order_dict
                    else:
                        self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单结果类型未知: {type(order)}")
                else:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单返回空列表")
            elif isinstance(result, dict):
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单结果是字典: {result}")
                return result
            elif hasattr(result, '__dict__'):
                order_dict = vars(result)
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单结果是对象: {order_dict}")
                return order_dict
            elif result is None:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单返回None")
            else:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单返回未知类型: {type(result)}, 值: {result}")
                
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单提交失败，无法解析结果")
            return None
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} {side_str}订单提交异常: {str(e)}")
            import traceback
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
            return None
        
    def _get_order_id(self, order):
        """提取订单ID"""
        try:
            if isinstance(order, dict):
                # 尝试多个可能的字段名
                for field in ['cl_ord_id', 'entrust_no', 'order_id', 'order_no']:
                    if field in order:
                        return order[field]
            elif hasattr(order, '__dict__'):
                order_dict = vars(order)
                for field in ['cl_ord_id', 'entrust_no', 'order_id', 'order_no']:
                    if field in order_dict:
                        return order_dict[field]
            return None
        except Exception as e:
            self.context.log.error(f"获取订单ID异常: {str(e)}")
            return None
            
    def update_cost_info(self, symbol, price, volume, is_sync=False):
        """更新成本信息"""
        try:
            if not hasattr(self.context, 'positions_cost'):
                self.context.positions_cost = {}
                
            # 计算交易成本
            commission = price * volume * self.commission_rate
            slippage = price * volume * self.slippage_rate
            
            # 获取当前时间
            now = self.context.now
            
            # 如果是新建仓位
            if symbol not in self.context.positions_cost:
                # 如果是同步持仓操作，使用过去的日期作为买入日期，避免覆盖实际买入日期
                # 使用一个明显的过去日期，以便区分
                if is_sync:
                    buy_time = now - datetime.timedelta(days=30)  # 使用30天前作为默认买入时间
                    buy_date = buy_time.strftime('%Y-%m-%d')
                    self.context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 同步持仓信息 {symbol}，使用估算买入日期: {buy_date}")
                else:
                    buy_time = now
                    buy_date = now.strftime('%Y-%m-%d')
                    self.context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 实际买入 {symbol}，记录实际买入日期: {buy_date}")
                
                self.context.positions_cost[symbol] = {
                    'symbol': symbol,
                    'cost_price': price,
                    'buy_time': buy_time,
                    'buy_date': buy_date,
                    'confirmed_high': price,
                    'confirmed_time': now,
                    'commission': commission,
                    'slippage': slippage,
                    'is_synced': is_sync  # 添加标记，记录这是同步操作还是实际买入
                }
                self.context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - {'同步' if is_sync else '新建'}仓位 {symbol} 成本价: {price:.3f}")
            else:
                # 已存在持仓信息
                old_info = self.context.positions_cost[symbol]
                
                # 如果之前是同步操作，现在是实际买入，则更新买入日期
                if is_sync == False and old_info.get('is_synced', False) == True:
                    old_info['buy_time'] = now
                    old_info['buy_date'] = now.strftime('%Y-%m-%d')
                    old_info['is_synced'] = False
                    self.context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 更新 {symbol} 买入日期: 从估算日期更新为实际买入日期 {old_info['buy_date']}")
                # 如果当前是同步操作，但已有记录不是同步操作，保留原有的买入日期
                elif is_sync == True and old_info.get('is_synced', True) == False:
                    self.context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 保留 {symbol} 的实际买入日期 {old_info['buy_date']}，不使用估算日期")
                    # 不更新买入日期，保留实际买入日期
                
                # 如果是加仓，计算加权平均成本
                old_volume = self.context.positions.get(symbol, 0) - volume  # 减去新买入的量
                
                if old_volume <= 0:
                    # 如果之前没有持仓（可能是数据不一致），直接使用新价格
                    new_cost = price
                else:
                    old_cost = old_info['cost_price']
                    new_cost = (old_cost * old_volume + price * volume) / (old_volume + volume)
                
                # 更新成本信息
                old_info['cost_price'] = new_cost
                
                # 不更新buy_time和buy_date，保留首次买入时间，除非是从同步状态转为实际买入
                
                # 更新最高价，如果新价格更高
                if price > old_info['confirmed_high']:
                    old_info['confirmed_high'] = price
                    old_info['confirmed_time'] = now
                
                # 累加交易成本
                old_info['commission'] = old_info.get('commission', 0) + commission
                old_info['slippage'] = old_info.get('slippage', 0) + slippage
                
                self.context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 更新 {symbol} 成本价: {new_cost:.3f} (原成本: {old_cost:.3f}, 新买入: {price:.3f})")
                
                # 添加日志记录当前买入日期信息
                self.context.log.debug(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 当前买入日期信息: 买入日期={old_info['buy_date']}, 买入时间={old_info['buy_time']}, 是否同步={old_info.get('is_synced', False)}")
                
            return self.context.positions_cost[symbol]
        except Exception as e:
            self.context.log.error(f"更新成本信息异常: {str(e)}")
            return None
            
    def _update_position_info(self, symbol, volume, order_result):
        """更新持仓信息"""
        if hasattr(self.context, 'positions'):
            current_holdings = len(self.context.positions)
            if symbol not in self.context.positions:
                self.context.positions[symbol] = 0
                position_change = "新增"
            else:
                position_change = "追加"
            
            self.context.positions[symbol] += volume
            
            # 提取订单价格
            order_price = 0
            if isinstance(order_result, dict):
                order_price = order_result.get('price', 0)
            elif hasattr(order_result, 'price'):
                order_price = getattr(order_result, 'price', 0)
            
            # 不再从order_result中获取buy_time，而是直接使用当前时间
            buy_time = self.context.now
            
            holding_days = 0  # 新买入的持仓天数为0
            
            high_price = order_price
            confirmed_time_obj = self.context.now
            
            # 获取 cost_info 中的 symbol, 如果不存在则使用传入的 symbol
            log_symbol = order_result.get('symbol', symbol)

            return {
                'position_change': position_change,
                'prev_holdings': current_holdings,
                'current_holdings': len(self.context.positions),
                'volume': volume,
                'price': order_price,
                'order_id': self._get_order_id(order_result)
            }
        return None
        
    def _log_order_error(self, order):
        """记录订单错误详情"""
        try:
            error_info = {
                'type': str(type(order)),
                'attrs': dir(order) if hasattr(order, '__dict__') else None,
                'content': str(order)[:200] if order else 'None'
            }
            self.context.log.error(f"订单解析失败 - 详细信息: {error_info}")
        except Exception as e:
            self.context.log.error(f"记录订单错误异常: {str(e)}")
        
    def _log_trade_stats(self, symbol, order_result, position_info):
        """记录交易统计"""
        try:
            # 提取订单价格
            order_price = 0
            if isinstance(order_result, dict):
                order_price = order_result.get('price', 0)
            elif hasattr(order_result, 'price'):
                order_price = getattr(order_result, 'price', 0)
                
            stats = {
                'symbol': symbol,
                'time': self.context.now.strftime('%Y-%m-%d %H:%M:%S'),
                'price': order_price,
                'volume': position_info['volume'],
                'order_id': position_info['order_id'],
                'position_change': position_info['position_change'],
                'holdings': position_info['current_holdings']
            }
            
            # 计算交易成本
            trade_value = stats['price'] * stats['volume']
            stats.update({
                'commission': trade_value * self.commission_rate,
                'slippage': trade_value * self.slippage_rate,
                'total_cost': trade_value * (1 + self.commission_rate + self.slippage_rate)
            })
            
            self.context.log.info(f"交易统计: {stats}")
            
        except Exception as e:
            self.context.log.error(f"记录交易统计失败: {str(e)}")

    def log_trade(self, symbol, reason, cost_info, position):
        """记录交易日志"""
        try:
            # 计算交易指标
            buy_price = cost_info['cost_price']
            sell_price = position['price']
            profit = sell_price - buy_price
            profit_pct = profit / buy_price * 100
            
            # 处理buy_time类型兼容性
            buy_time_val = cost_info.get('buy_time') 
            if not buy_time_val:
                self.context.log.error(f"log_trade: cost_info for {symbol} is missing 'buy_time'. cost_info: {cost_info}")
                # 使用一个默认的过去时间，以避免程序中断，但标记问题
                buy_time_val = self.context.now - datetime.timedelta(days=999)

            if isinstance(buy_time_val, str):
                try:
                    buy_time_val = datetime.datetime.strptime(buy_time_val, '%Y-%m-%d %H:%M:%S.%f')
                except ValueError:
                    try:
                        buy_time_val = datetime.datetime.strptime(buy_time_val, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            buy_time_val = datetime.datetime.strptime(buy_time_val, '%Y-%m-%d %H:%M')
                        except ValueError as e_time:
                            self.context.log.error(f"log_trade: Error parsing buy_time string '{cost_info.get('buy_time')}' for {symbol}: {e_time}")
                            buy_time_val = self.context.now - datetime.timedelta(days=999) # Fallback
            
            if not isinstance(buy_time_val, datetime.datetime):
                self.context.log.error(f"log_trade: buy_time for {symbol} is not a datetime object after parsing. Type: {type(buy_time_val)}. cost_info: {cost_info}")
                buy_time_val = self.context.now - datetime.timedelta(days=999) # Fallback
                
            # 确保datetime对象没有时区信息 - 修复时区问题
            context_now = self.context.now
            if hasattr(context_now, 'tzinfo') and context_now.tzinfo is not None:
                context_now = context_now.replace(tzinfo=None)
                
            if hasattr(buy_time_val, 'tzinfo') and buy_time_val.tzinfo is not None:
                buy_time_val = buy_time_val.replace(tzinfo=None)

            holding_days = (context_now - buy_time_val).days
            
            high_price = cost_info.get('confirmed_high', buy_price)
            confirmed_time_obj = cost_info.get('confirmed_time', self.context.now)

            if isinstance(confirmed_time_obj, str):
                 try:
                    confirmed_time_obj = datetime.datetime.strptime(confirmed_time_obj, '%Y-%m-%d %H:%M:%S.%f')
                 except ValueError:
                    try:
                        confirmed_time_obj = datetime.datetime.strptime(confirmed_time_obj, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        confirmed_time_obj = self.context.now # Fallback
            elif not isinstance(confirmed_time_obj, datetime.datetime):
                confirmed_time_obj = self.context.now # Fallback
                
            # 确保confirmed_time_obj也没有时区信息
            if hasattr(confirmed_time_obj, 'tzinfo') and confirmed_time_obj.tzinfo is not None:
                confirmed_time_obj = confirmed_time_obj.replace(tzinfo=None)
            
            # 获取 cost_info 中的 symbol, 如果不存在则使用传入的 symbol
            log_symbol = cost_info.get('symbol', symbol)
            
            # 计算交易成本
            total_commission = cost_info.get('commission', 0)
            total_slippage = cost_info.get('slippage', 0)
            # net_profit is ambiguous as it's not clear if position['volume'] is the full or partial sell
            # The P/L should ideally be calculated on the actual filled volume and price from execution report for sells.
            # Here, we use the information available in cost_info which is typically for the initial buy.
            net_profit_value = profit * position['volume'] - total_commission - total_slippage # Example calculation based on full volume
            
            # 计算净利润百分比
            net_profit_pct = 0
            if buy_price * position['volume'] != 0:
                net_profit_pct = (net_profit_value / (buy_price * position['volume'])) * 100
            
            # 计算最大利润百分比
            max_profit_pct = 0
            if buy_price != 0:
                max_profit_pct = (high_price - buy_price) / buy_price * 100
            
            # 计算最终回撤百分比
            final_drawdown_pct = 0
            if high_price != 0:
                final_drawdown_pct = (sell_price - high_price) / high_price * 100
            
            # 记录交易日志
            self.context.log.info(f"""
            {self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 卖出 {log_symbol}:
            卖出原因: {reason}
            卖出价格: {sell_price:.3f}
            买入价格: {buy_price:.3f}
            历史最高: {high_price:.3f} ({confirmed_time_obj.strftime('%Y-%m-%d %H:%M:%S')})
            持仓天数: {holding_days}
            净利润: {net_profit_pct:.2f}%
            最大利润: {max_profit_pct:.2f}%
            最终回撤: {final_drawdown_pct:.2f}%
            交易成本: 手续费{total_commission:.2f}, 滑点{total_slippage:.2f}
            """)
            
            # 返回交易日志数据，以便调用者进一步处理
            return {
                'symbol': log_symbol,
                'sell_reason': reason,
                'sell_price': sell_price,
                'buy_price': buy_price,
                'high_price': high_price,
                'holding_days': holding_days,
                'net_profit_pct': net_profit_pct,
                'max_profit_pct': max_profit_pct,
                'final_drawdown_pct': final_drawdown_pct
            }
            
        except Exception as e:
            self.context.log.error(f"记录交易日志异常: {str(e)}")
            return None

    def _get_config_value(self, param_name, default=None):
        """
        从context获取配置参数值，支持动态配置和静态配置
        """
        # 优先从context的get_config_value方法获取
        if hasattr(self.context, 'get_config_value'):
            return self.context.get_config_value(param_name, default)
        
        # 其次从context.config获取
        elif hasattr(self.context, 'config') and hasattr(self.context.config, param_name):
            return getattr(self.context.config, param_name)
        
        # 最后返回默认值
        return default

    def _save_buy_record(self, symbol, price, volume, order_result):
        """保存买入记录到数据库"""
        try:
            # 获取当前的技术指标数据
            buy_data = self._get_buy_indicators(symbol)

            # 构建买入记录
            trade_record = {
                'symbol': symbol,
                'action': 'BUY',
                'price': price,
                'volume': volume,
                'timestamp': self.context.now.strftime('%Y-%m-%d %H:%M:%S'),
                'order_id': getattr(order_result, 'cl_ord_id', ''),
                **buy_data  # 添加技术指标数据
            }

            # 保存到数据库
            if hasattr(self.context, 'data_manager') and self.context.data_manager:
                self.context.data_manager.log_trade(trade_record)
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 💾 {symbol} 买入记录已保存到数据库")
            else:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - ⚠️ 数据管理器不可用，买入记录未保存")

        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 保存买入记录异常: {str(e)}")

    def _get_buy_indicators(self, symbol):
        """获取买入时的技术指标数据"""
        try:
            # 获取历史数据
            data = history_n(symbol=symbol, count=60, frequency='1d',
                           fields=['open', 'high', 'low', 'close', 'volume'],
                           end_time=self.context.now)

            if data is None or len(data) < 30:
                return {}

            # 转换为DataFrame格式
            if isinstance(data, list):
                import pandas as pd
                data = pd.DataFrame(data)

            # 计算技术指标
            indicators = {}
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            volumes = data['volume'].values

            # 使用talib计算指标
            try:
                import talib
                import numpy as np

                # RSI指标
                indicators['rsi_14d'] = talib.RSI(close_prices, timeperiod=14)[-1] if len(close_prices) >= 14 else None
                indicators['rsi_5d'] = talib.RSI(close_prices, timeperiod=5)[-1] if len(close_prices) >= 5 else None

                # 移动平均线
                indicators['ma_5d'] = talib.SMA(close_prices, timeperiod=5)[-1] if len(close_prices) >= 5 else None
                indicators['ma_20d'] = talib.SMA(close_prices, timeperiod=20)[-1] if len(close_prices) >= 20 else None

                # MACD
                macd, macd_signal, macd_hist = talib.MACD(close_prices)
                indicators['macd_line'] = macd[-1] if len(macd) > 0 and not np.isnan(macd[-1]) else None
                indicators['macd_signal'] = macd_signal[-1] if len(macd_signal) > 0 and not np.isnan(macd_signal[-1]) else None
                indicators['macd_histogram'] = macd_hist[-1] if len(macd_hist) > 0 and not np.isnan(macd_hist[-1]) else None

                # 布林带
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close_prices, timeperiod=20)
                indicators['bb_upper_20'] = bb_upper[-1] if len(bb_upper) > 0 and not np.isnan(bb_upper[-1]) else None
                indicators['bb_lower_20'] = bb_lower[-1] if len(bb_lower) > 0 and not np.isnan(bb_lower[-1]) else None

                # ATR
                indicators['atr_14d'] = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)[-1] if len(close_prices) >= 14 else None

                # 成交量比率
                if len(volumes) >= 5:
                    vol_ma5 = talib.SMA(volumes, timeperiod=5)[-1]
                    indicators['volume_ma5_ratio'] = volumes[-1] / vol_ma5 if vol_ma5 > 0 else None

            except ImportError:
                # 如果没有talib，使用简化计算
                import numpy as np
                indicators['ma_5d'] = np.mean(close_prices[-5:]) if len(close_prices) >= 5 else None
                indicators['ma_20d'] = np.mean(close_prices[-20:]) if len(close_prices) >= 20 else None

            # 过滤掉None值和NaN值
            indicators = {k: v for k, v in indicators.items() if v is not None and not (isinstance(v, float) and np.isnan(v))}

            return indicators

        except Exception as e:
            self.context.log.debug(f"获取买入指标异常: {str(e)}")
            return {}
