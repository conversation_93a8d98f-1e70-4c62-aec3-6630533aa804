# coding=utf-8
"""
高级性能优化器
大幅度提升回测效率，不影响买入卖出逻辑
"""

import time
import numpy as np
import pandas as pd
from collections import defaultdict, deque
import threading
import gc
from functools import lru_cache
import pickle
import os

class AdvancedPerformanceOptimizer:
    """高级性能优化器"""
    
    def __init__(self, context):
        self.context = context
        self.config = self._get_config()
        
        # 缓存系统
        self.data_cache = {}
        self.symbol_cache = {}
        self.calculation_cache = {}
        self.trix_cache = {}
        
        # 性能统计
        self.performance_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'data_fetch_time': 0,
            'calculation_time': 0,
            'total_optimizations': 0
        }
        
        # 内存池
        self.memory_pool = deque(maxlen=self.config.get('memory_pool_size', 50))
        
        # 初始化优化器
        self._initialize_optimizer()
    
    def _get_config(self):
        """获取性能优化配置"""
        try:
            from config import get_config_value
            return get_config_value('PERFORMANCE_OPTIMIZATION', {})
        except:
            return {}
    
    def _initialize_optimizer(self):
        """初始化优化器"""
        if self.config.get('enable_symbol_cache', True):
            self._preload_symbol_lists()
        
        if self.config.get('enable_memory_pool', True):
            self._initialize_memory_pool()
        
        self.context.log.info(f"高级性能优化器初始化完成")
    
    def _preload_symbol_lists(self):
        """预加载股票列表"""
        try:
            # 缓存常用的股票列表
            cache_key = f"symbol_list_{self.context.now.strftime('%Y-%m-%d')}"
            
            if cache_key not in self.symbol_cache:
                # 这里可以预加载指数成分股等
                self.symbol_cache[cache_key] = {
                    'timestamp': time.time(),
                    'symbols': []
                }
                
        except Exception as e:
            self.context.log.debug(f"预加载股票列表异常: {e}")
    
    def _initialize_memory_pool(self):
        """初始化内存池"""
        try:
            pool_size = self.config.get('memory_pool_size', 50)
            for _ in range(pool_size):
                self.memory_pool.append(np.zeros(100))  # 预分配数组
        except Exception as e:
            self.context.log.debug(f"内存池初始化异常: {e}")
    
    def get_cached_data(self, symbol, count, frequency='1d', fields=None):
        """获取缓存的历史数据"""
        if not self.config.get('enable_data_preload', True):
            return None
        
        cache_key = f"{symbol}_{count}_{frequency}_{fields}"
        current_time = time.time()
        
        # 检查缓存
        if cache_key in self.data_cache:
            cache_entry = self.data_cache[cache_key]
            # 检查缓存是否过期（5分钟）
            if current_time - cache_entry['timestamp'] < 300:
                self.performance_stats['cache_hits'] += 1
                return cache_entry['data']
        
        self.performance_stats['cache_misses'] += 1
        return None
    
    def cache_data(self, symbol, count, frequency, fields, data):
        """缓存历史数据"""
        if not self.config.get('enable_data_preload', True):
            return
        
        cache_key = f"{symbol}_{count}_{frequency}_{fields}"
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
        
        # 限制缓存大小
        if len(self.data_cache) > 1000:
            # 删除最旧的缓存
            oldest_key = min(self.data_cache.keys(), 
                           key=lambda k: self.data_cache[k]['timestamp'])
            del self.data_cache[oldest_key]
    
    def get_cached_calculation(self, calc_type, symbol, params):
        """获取缓存的计算结果"""
        if not self.config.get('enable_calculation_cache', True):
            return None
        
        cache_key = f"{calc_type}_{symbol}_{hash(str(params))}"
        
        if cache_key in self.calculation_cache:
            self.performance_stats['cache_hits'] += 1
            return self.calculation_cache[cache_key]
        
        self.performance_stats['cache_misses'] += 1
        return None
    
    def cache_calculation(self, calc_type, symbol, params, result):
        """缓存计算结果"""
        if not self.config.get('enable_calculation_cache', True):
            return
        
        cache_key = f"{calc_type}_{symbol}_{hash(str(params))}"
        self.calculation_cache[cache_key] = result
        
        # 限制缓存大小
        cache_size_limit = self.config.get('calculation_cache_size', 10000)
        if len(self.calculation_cache) > cache_size_limit:
            # 删除一半的缓存
            keys_to_delete = list(self.calculation_cache.keys())[:len(self.calculation_cache)//2]
            for key in keys_to_delete:
                del self.calculation_cache[key]
    
    def vectorized_trix_calculation(self, symbols_data):
        """向量化TRIX计算"""
        if not self.config.get('enable_vectorized_calculation', True):
            return {}
        
        try:
            import talib
            results = {}
            
            # 批量计算TRIX
            for symbol, data in symbols_data.items():
                if len(data) >= 10:
                    close_prices = np.array(data, dtype=np.float64)
                    trix = talib.TRIX(close_prices, timeperiod=3)
                    results[symbol] = trix
            
            return results
            
        except Exception as e:
            self.context.log.debug(f"向量化TRIX计算异常: {e}")
            return {}
    
    def batch_data_preload(self, symbols, count=60):
        """批量数据预加载"""
        if not self.config.get('enable_data_preload', True):
            return {}
        
        try:
            start_time = time.time()
            
            # 检查哪些数据需要获取
            symbols_to_fetch = []
            for symbol in symbols:
                cached_data = self.get_cached_data(symbol, count)
                if cached_data is None:
                    symbols_to_fetch.append(symbol)
            
            if not symbols_to_fetch:
                return {}
            
            # 批量获取数据
            batch_size = self.config.get('batch_size', 1000)
            all_data = {}
            
            for i in range(0, len(symbols_to_fetch), batch_size):
                batch_symbols = symbols_to_fetch[i:i + batch_size]
                
                try:
                    # 尝试批量获取
                    from main import history_n
                    batch_data = history_n(
                        symbol=batch_symbols, 
                        count=count, 
                        frequency='1d', 
                        fields=['close'], 
                        end_time=self.context.now
                    )
                    
                    if isinstance(batch_data, dict):
                        all_data.update(batch_data)
                        
                        # 缓存数据
                        for symbol, data in batch_data.items():
                            self.cache_data(symbol, count, '1d', ['close'], data)
                    
                except Exception as e:
                    self.context.log.debug(f"批量数据获取异常: {e}")
                    # 回退到单个获取
                    for symbol in batch_symbols:
                        try:
                            data = history_n(
                                symbol=symbol, 
                                count=count, 
                                frequency='1d', 
                                fields=['close'], 
                                end_time=self.context.now
                            )
                            if data is not None:
                                all_data[symbol] = data
                                self.cache_data(symbol, count, '1d', ['close'], data)
                        except:
                            continue
            
            fetch_time = time.time() - start_time
            self.performance_stats['data_fetch_time'] += fetch_time
            
            if len(all_data) > 0:
                self.context.log.info(f"批量预加载完成: {len(all_data)}只股票, 耗时: {fetch_time:.2f}秒")
            
            return all_data
            
        except Exception as e:
            self.context.log.debug(f"批量数据预加载异常: {e}")
            return {}
    
    def cleanup_cache(self):
        """清理过期缓存"""
        try:
            current_time = time.time()
            cleanup_interval = self.config.get('cache_cleanup_interval', 300)
            
            # 清理数据缓存
            expired_keys = []
            for key, entry in self.data_cache.items():
                if current_time - entry['timestamp'] > cleanup_interval:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.data_cache[key]
            
            # 强制垃圾回收
            if len(expired_keys) > 0:
                gc.collect()
                self.context.log.debug(f"清理了 {len(expired_keys)} 个过期缓存")
                
        except Exception as e:
            self.context.log.debug(f"缓存清理异常: {e}")
    
    def get_performance_stats(self):
        """获取性能统计"""
        total_requests = self.performance_stats['cache_hits'] + self.performance_stats['cache_misses']
        hit_rate = (self.performance_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hit_rate': f"{hit_rate:.1f}%",
            'total_requests': total_requests,
            'data_fetch_time': f"{self.performance_stats['data_fetch_time']:.2f}s",
            'calculation_time': f"{self.performance_stats['calculation_time']:.2f}s",
            'cache_size': len(self.data_cache),
            'calculation_cache_size': len(self.calculation_cache)
        }
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        try:
            # 清理不必要的变量
            gc.collect()
            
            # 压缩缓存
            if len(self.data_cache) > 500:
                # 保留最近的一半缓存
                sorted_cache = sorted(
                    self.data_cache.items(),
                    key=lambda x: x[1]['timestamp'],
                    reverse=True
                )
                
                self.data_cache = dict(sorted_cache[:len(sorted_cache)//2])
                
        except Exception as e:
            self.context.log.debug(f"内存优化异常: {e}")
