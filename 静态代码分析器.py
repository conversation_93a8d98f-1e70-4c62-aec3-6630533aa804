#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静态代码分析器
识别代码中的潜在问题和优化机会
"""

import re
import ast
import os
from collections import defaultdict, Counter

class CodeAnalyzer:
    def __init__(self, file_path):
        self.file_path = file_path
        with open(file_path, 'r', encoding='utf-8') as f:
            self.content = f.read()
        self.lines = self.content.split('\n')
        
    def analyze_function_complexity(self):
        """分析函数复杂度"""
        print("🔍 分析函数复杂度...")
        
        # 查找所有函数定义
        function_pattern = r'^def\s+(\w+)\s*\('
        functions = []
        
        current_function = None
        function_lines = 0
        indent_level = 0
        
        for i, line in enumerate(self.lines):
            stripped = line.strip()
            if not stripped or stripped.startswith('#'):
                continue
                
            # 检查函数定义
            match = re.match(function_pattern, stripped)
            if match:
                if current_function:
                    functions.append({
                        'name': current_function,
                        'lines': function_lines,
                        'start_line': i - function_lines + 1
                    })
                current_function = match.group(1)
                function_lines = 1
                indent_level = len(line) - len(line.lstrip())
            elif current_function:
                # 检查是否还在函数内部
                if line.strip() and (len(line) - len(line.lstrip())) > indent_level:
                    function_lines += 1
                elif line.strip() and not line.startswith(' ' * (indent_level + 1)):
                    # 函数结束
                    functions.append({
                        'name': current_function,
                        'lines': function_lines,
                        'start_line': i - function_lines + 1
                    })
                    current_function = None
                    function_lines = 0
        
        # 添加最后一个函数
        if current_function:
            functions.append({
                'name': current_function,
                'lines': function_lines,
                'start_line': len(self.lines) - function_lines + 1
            })
        
        # 按行数排序，找出复杂函数
        functions.sort(key=lambda x: x['lines'], reverse=True)
        
        print(f"📊 发现 {len(functions)} 个函数")
        print("🚨 最复杂的10个函数:")
        for i, func in enumerate(functions[:10]):
            complexity = "🔴 极高" if func['lines'] > 200 else "🟡 较高" if func['lines'] > 100 else "🟢 正常"
            print(f"  {i+1:2d}. {func['name']:<30} {func['lines']:3d}行 {complexity}")
        
        return functions
    
    def analyze_duplicate_code_blocks(self):
        """分析重复代码块"""
        print("\n🔍 分析重复代码块...")
        
        # 查找相似的代码块（去除空行和注释）
        code_blocks = defaultdict(list)
        
        for i in range(len(self.lines) - 5):  # 至少5行的代码块
            block = []
            for j in range(5):  # 检查5行代码块
                line = self.lines[i + j].strip()
                if line and not line.startswith('#'):
                    # 标准化代码行（去除变量名差异）
                    normalized = re.sub(r'\b\w+\b', 'VAR', line)
                    normalized = re.sub(r'\d+', 'NUM', normalized)
                    block.append(normalized)
            
            if len(block) >= 3:  # 至少3行有效代码
                block_key = '\n'.join(block)
                code_blocks[block_key].append(i + 1)
        
        # 找出重复的代码块
        duplicates = {k: v for k, v in code_blocks.items() if len(v) > 1}
        
        print(f"📊 发现 {len(duplicates)} 个重复代码块")
        if duplicates:
            print("🚨 重复代码块 (前5个):")
            for i, (block, lines) in enumerate(list(duplicates.items())[:5]):
                print(f"  {i+1}. 出现在行: {lines}")
                print(f"     代码片段: {block.split()[0][:50]}...")
        
        return duplicates
    
    def analyze_long_parameter_lists(self):
        """分析长参数列表"""
        print("\n🔍 分析长参数列表...")
        
        function_pattern = r'def\s+(\w+)\s*\(([^)]*)\)'
        long_params = []
        
        for i, line in enumerate(self.lines):
            match = re.search(function_pattern, line)
            if match:
                func_name = match.group(1)
                params_str = match.group(2)
                
                # 计算参数数量
                if params_str.strip():
                    params = [p.strip() for p in params_str.split(',') if p.strip()]
                    param_count = len(params)
                    
                    if param_count > 5:  # 超过5个参数认为过多
                        long_params.append({
                            'name': func_name,
                            'param_count': param_count,
                            'line': i + 1,
                            'params': params
                        })
        
        long_params.sort(key=lambda x: x['param_count'], reverse=True)
        
        print(f"📊 发现 {len(long_params)} 个长参数列表函数")
        if long_params:
            print("🚨 参数过多的函数:")
            for func in long_params:
                print(f"  {func['name']:<30} {func['param_count']}个参数 (行{func['line']})")
        
        return long_params
    
    def analyze_magic_numbers(self):
        """分析魔法数字"""
        print("\n🔍 分析魔法数字...")
        
        # 查找代码中的数字常量（排除常见的0, 1, -1等）
        magic_numbers = defaultdict(list)
        
        for i, line in enumerate(self.lines):
            # 跳过注释和字符串
            if line.strip().startswith('#'):
                continue
                
            # 查找数字
            numbers = re.findall(r'\b(\d+\.?\d*)\b', line)
            for num in numbers:
                # 排除常见的非魔法数字
                if num not in ['0', '1', '2', '10', '100', '1000']:
                    magic_numbers[num].append(i + 1)
        
        # 找出出现频率高的数字
        frequent_numbers = {k: v for k, v in magic_numbers.items() if len(v) > 2}
        
        print(f"📊 发现 {len(frequent_numbers)} 个可能的魔法数字")
        if frequent_numbers:
            print("🚨 频繁出现的数字常量:")
            for num, lines in sorted(frequent_numbers.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
                print(f"  {num:<10} 出现{len(lines)}次 在行: {lines[:5]}{'...' if len(lines) > 5 else ''}")
        
        return frequent_numbers
    
    def analyze_nested_complexity(self):
        """分析嵌套复杂度"""
        print("\n🔍 分析嵌套复杂度...")
        
        high_nesting = []
        
        for i, line in enumerate(self.lines):
            # 计算缩进级别
            if line.strip():
                indent_level = (len(line) - len(line.lstrip())) // 4
                if indent_level > 4:  # 超过4级嵌套
                    high_nesting.append({
                        'line': i + 1,
                        'level': indent_level,
                        'content': line.strip()[:50]
                    })
        
        print(f"📊 发现 {len(high_nesting)} 行高嵌套代码")
        if high_nesting:
            print("🚨 高嵌套代码 (前10行):")
            for item in high_nesting[:10]:
                print(f"  行{item['line']:4d}: {item['level']}级嵌套 - {item['content']}")
        
        return high_nesting
    
    def analyze_import_usage(self):
        """分析导入使用情况"""
        print("\n🔍 分析导入使用情况...")
        
        imports = []
        import_pattern = r'^(?:from\s+(\S+)\s+)?import\s+(.+)$'
        
        for i, line in enumerate(self.lines):
            stripped = line.strip()
            match = re.match(import_pattern, stripped)
            if match:
                module = match.group(1) or 'builtin'
                items = [item.strip() for item in match.group(2).split(',')]
                for item in items:
                    # 处理 as 别名
                    if ' as ' in item:
                        original, alias = item.split(' as ')
                        imports.append({
                            'module': module,
                            'name': original.strip(),
                            'alias': alias.strip(),
                            'line': i + 1
                        })
                    else:
                        imports.append({
                            'module': module,
                            'name': item.strip(),
                            'alias': None,
                            'line': i + 1
                        })
        
        # 检查使用情况
        unused_imports = []
        for imp in imports:
            search_name = imp['alias'] if imp['alias'] else imp['name']
            if search_name != '*':  # 跳过 import *
                # 在代码中搜索使用情况
                usage_count = 0
                for line in self.lines:
                    if re.search(r'\b' + re.escape(search_name) + r'\b', line):
                        usage_count += 1
                
                if usage_count <= 1:  # 只在导入行出现
                    unused_imports.append(imp)
        
        print(f"📊 总导入数: {len(imports)}")
        print(f"📊 可能未使用的导入: {len(unused_imports)}")
        if unused_imports:
            print("🚨 可能未使用的导入:")
            for imp in unused_imports[:10]:
                module_info = f"from {imp['module']} " if imp['module'] != 'builtin' else ""
                alias_info = f" as {imp['alias']}" if imp['alias'] else ""
                print(f"  行{imp['line']:3d}: {module_info}import {imp['name']}{alias_info}")
        
        return unused_imports

def main():
    """主函数"""
    print("🚀 开始静态代码分析")
    print("=" * 60)
    
    file_path = "main.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件 {file_path} 不存在")
        return
    
    analyzer = CodeAnalyzer(file_path)
    
    # 执行各种分析
    functions = analyzer.analyze_function_complexity()
    duplicates = analyzer.analyze_duplicate_code_blocks()
    long_params = analyzer.analyze_long_parameter_lists()
    magic_numbers = analyzer.analyze_magic_numbers()
    high_nesting = analyzer.analyze_nested_complexity()
    unused_imports = analyzer.analyze_import_usage()
    
    # 生成总结报告
    print("\n" + "=" * 60)
    print("📋 分析总结:")
    print(f"  📁 文件: {file_path}")
    print(f"  📏 总行数: {len(analyzer.lines):,}")
    print(f"  🔧 函数数量: {len(functions)}")
    print(f"  🚨 复杂函数: {len([f for f in functions if f['lines'] > 100])}")
    print(f"  🔄 重复代码块: {len(duplicates)}")
    print(f"  📝 长参数列表: {len(long_params)}")
    print(f"  🔢 魔法数字: {len(magic_numbers)}")
    print(f"  🏗️ 高嵌套行: {len(high_nesting)}")
    print(f"  📦 未使用导入: {len(unused_imports)}")
    
    # 优化建议
    print("\n💡 优化建议:")
    if len([f for f in functions if f['lines'] > 200]) > 0:
        print("  🔴 建议拆分超大函数 (>200行)")
    if len(duplicates) > 5:
        print("  🟡 建议提取重复代码为公共函数")
    if len(long_params) > 0:
        print("  🟡 建议使用配置对象替代长参数列表")
    if len(high_nesting) > 10:
        print("  🟡 建议降低代码嵌套层级")
    if len(unused_imports) > 5:
        print("  🟢 建议清理未使用的导入")
    
    print("\n✅ 静态分析完成！")

if __name__ == "__main__":
    main()
