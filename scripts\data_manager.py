#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import csv
import pandas as pd
import sqlite3
import shutil
import logging
from datetime import datetime
import numpy as np
# 导入日志管理器
from logger_manager import setup_logger, get_data_manager_logger

# 配置日志 - 使用日志管理器
logger = setup_logger('data_manager_log', 'data_manager.log')

# 统一定义数据路径常量
DATA_DIR = 'data'
REPORTS_DIR = 'reports'
LOG_DIR = 'logs'
BACKUP_DIR = 'backups'

# 确保目录存在
for directory in [DATA_DIR, REPORTS_DIR, LOG_DIR, BACKUP_DIR]:
    os.makedirs(directory, exist_ok=True)

# 定义文件路径
TRADE_LOG_FILE = os.path.join(DATA_DIR, 'trade_log.csv')
ANALYSIS_LOG_FILE = os.path.join(DATA_DIR, 'analysis_log.csv')
TRADE_RESULTS_FILE = os.path.join(REPORTS_DIR, 'trade_analysis_results.csv')
DB_FILE = os.path.join(DATA_DIR, 'trades.db')

# 定义字段名
TRADE_FIELDNAMES = [
    'Timestamp', 'Symbol', 'Action', 'Price', 'Volume',
    'Trend_Strength_Buy', 'MA3_Buy', 'MA7_Buy', 'MA_Cross_Buy_Signal_Buy',
    'Sell_Reason', 'Cost_Price_Sell', 'Net_Profit_Pct_Sell', 'Confirmed_High_Sell',
    'Confirmed_High_Time', 'Holding_Hours', 'Max_Profit_Pct', 'Final_Drawdown_Pct',
    'TRIX_Buy', 'Volatility', 'ATR_Pct', 'Volatility_Score', 'Allocation_Factor',
    'ADX', 'DMI_Plus', 'DMI_Minus', 'BB_Width', 'BB_Position',
    'RSI', 'CCI', 'MACD', 'MACD_Signal', 'MACD_Hist',
    'Relative_Volume', 'Volume_Change_Rate', 'OBV',
    'Industry_Relative_Strength', 'Market_Correlation',
    'PE_Ratio', 'PB_Ratio', 'ROE',
    'Distance_From_High', 'MA_System_Status'
]

ANALYSIS_FIELDNAMES = [
    'Timestamp', 'Symbol', 'Current_Price', 'MA3', 'MA7', 'MA20',
    'RSI', 'MACD', 'KDJ_K', 'KDJ_D', 'Boll_Middle',
    'Volume_Ratio', 'Trend_Strength', 'MA_Cross_Buy_Signal'
]

class DataManager:
    """数据管理类，统一管理数据访问和存储"""
    
    def __init__(self, use_db=True, use_parquet=False):
        """初始化数据管理器
        
        Args:
            use_db: 是否使用数据库存储
            use_parquet: 是否使用Parquet格式存储
        """
        self.use_db = use_db
        self.use_parquet = use_parquet
        self.cache = {}
        self.cache_size = 100
        
        # 初始化数据存储
        if self.use_db:
            self._init_db()
        
        # 确保CSV文件存在
        self._init_csv_files()
    
    def _init_db(self):
        """初始化数据库结构"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # 创建交易表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                action TEXT,
                price REAL,
                volume INTEGER,
                trend_strength_buy REAL,
                ma3_buy REAL,
                ma7_buy REAL,
                ma_cross_buy_signal_buy INTEGER,
                sell_reason TEXT,
                cost_price_sell REAL,
                net_profit_pct_sell REAL,
                confirmed_high_sell REAL,
                confirmed_high_time TEXT,
                holding_hours REAL,
                max_profit_pct REAL,
                final_drawdown_pct REAL,
                trix_buy REAL,
                volatility REAL,
                atr_pct REAL,
                volatility_score REAL,
                allocation_factor REAL,
                adx REAL,
                dmi_plus REAL,
                dmi_minus REAL,
                bb_width REAL,
                bb_position REAL,
                rsi REAL,
                cci REAL,
                macd REAL,
                macd_signal REAL,
                macd_hist REAL,
                relative_volume REAL,
                volume_change_rate REAL,
                obv REAL,
                industry_relative_strength REAL,
                market_correlation REAL,
                pe_ratio REAL,
                pb_ratio REAL,
                roe REAL,
                distance_from_high REAL,
                ma_system_status INTEGER,
                status TEXT DEFAULT 'ACTIVE'
            )
            ''')
            
            # 创建分析表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                current_price REAL,
                ma3 REAL,
                ma7 REAL,
                ma20 REAL,
                rsi REAL,
                macd REAL,
                kdj_k REAL,
                kdj_d REAL,
                boll_middle REAL,
                volume_ratio REAL,
                trend_strength REAL,
                ma_cross_buy_signal INTEGER,
                trix_buy_signal INTEGER,
                final_buy_signal INTEGER,
                profit REAL DEFAULT 0,
                nav REAL DEFAULT 1.0,
                strategy_yield REAL DEFAULT 0
            )
            ''')
            
            # 检查并更新表结构
            self._check_and_update_table_structure(conn)
            
            # 创建索引以提高查询效率
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades (timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_symbol ON analysis (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_timestamp ON analysis (timestamp)')
            
            conn.commit()
            conn.close()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"初始化数据库失败: {str(e)}", exc_info=True)
    
    def _check_and_update_table_structure(self, conn):
        """检查并更新表结构，确保所有必要的字段都存在"""
        try:
            cursor = conn.cursor()
            
            # 检查交易表结构
            cursor.execute("PRAGMA table_info(trades)")
            existing_trade_columns = {row[1].lower(): row[2] for row in cursor.fetchall()}
            
            # 定义交易表应该有的列及其类型
            expected_trade_columns = {
                'id': 'INTEGER',
                'timestamp': 'TEXT',
                'symbol': 'TEXT',
                'action': 'TEXT',
                'price': 'REAL',
                'volume': 'INTEGER',
                'trend_strength_buy': 'REAL',
                'ma3_buy': 'REAL',
                'ma7_buy': 'REAL',
                'ma_cross_buy_signal_buy': 'INTEGER',
                'sell_reason': 'TEXT',
                'cost_price_sell': 'REAL',
                'net_profit_pct_sell': 'REAL',
                'confirmed_high_sell': 'REAL',
                'confirmed_high_time': 'TEXT',
                'holding_hours': 'REAL',
                'max_profit_pct': 'REAL',
                'final_drawdown_pct': 'REAL',
                'trix_buy': 'REAL',
                'volatility': 'REAL',
                'atr_pct': 'REAL',
                'volatility_score': 'REAL',
                'allocation_factor': 'REAL',
                'adx': 'REAL',
                'dmi_plus': 'REAL',
                'dmi_minus': 'REAL',
                'bb_width': 'REAL',
                'bb_position': 'REAL',
                'rsi': 'REAL',
                'cci': 'REAL',
                'macd': 'REAL',
                'macd_signal': 'REAL',
                'macd_hist': 'REAL',
                'relative_volume': 'REAL',
                'volume_change_rate': 'REAL',
                'obv': 'REAL',
                'industry_relative_strength': 'REAL',
                'market_correlation': 'REAL',
                'pe_ratio': 'REAL',
                'pb_ratio': 'REAL',
                'roe': 'REAL',
                'distance_from_high': 'REAL',
                'ma_system_status': 'INTEGER',
                'amplitude_pct': 'REAL',
                'ma_period': 'INTEGER',
                'ma_direction': 'TEXT',
                'ma_value': 'REAL',
                'status': 'TEXT'
            }
            
            # 添加缺失的列
            for column, column_type in expected_trade_columns.items():
                if column.lower() not in existing_trade_columns:
                    logger.info(f"向trades表添加缺失的列: {column} ({column_type})")
                    cursor.execute(f"ALTER TABLE trades ADD COLUMN {column} {column_type}")
            
            # 检查分析表结构
            cursor.execute("PRAGMA table_info(analysis)")
            existing_analysis_columns = {row[1].lower(): row[2] for row in cursor.fetchall()}
            
            # 定义分析表应该有的列及其类型
            expected_analysis_columns = {
                'id': 'INTEGER',
                'timestamp': 'TEXT',
                'symbol': 'TEXT',
                'current_price': 'REAL',
                'ma3': 'REAL',
                'ma7': 'REAL',
                'ma20': 'REAL',
                'rsi': 'REAL',
                'macd': 'REAL',
                'kdj_k': 'REAL',
                'kdj_d': 'REAL',
                'boll_middle': 'REAL',
                'volume_ratio': 'REAL',
                'trend_strength': 'REAL',
                'ma_cross_buy_signal': 'INTEGER',
                'trix_buy_signal': 'INTEGER',
                'final_buy_signal': 'INTEGER',
                'profit': 'REAL',
                'nav': 'REAL',
                'strategy_yield': 'REAL'
            }
            
            # 添加缺失的列
            for column, column_type in expected_analysis_columns.items():
                if column.lower() not in existing_analysis_columns:
                    logger.info(f"向analysis表添加缺失的列: {column} ({column_type})")
                    cursor.execute(f"ALTER TABLE analysis ADD COLUMN {column} {column_type}")
            
            conn.commit()
            logger.info("表结构检查和更新完成")
        except Exception as e:
            logger.error(f"检查和更新表结构失败: {str(e)}", exc_info=True)
    
    def _init_csv_files(self):
        """初始化CSV文件 - 该方法保留但不再创建CSV文件"""
        # 不再创建CSV文件，只记录日志
        logger.info("CSV文件功能已禁用，只使用数据库存储数据")
    
    def save_trade(self, trade_data):
        """保存交易记录
        
        Args:
            trade_data: 包含交易信息的字典
        """
        try:
            # 验证交易数据
            if not self._validate_trade_data(trade_data):
                logger.warning("交易数据验证失败，跳过保存")
                return False
            
            # 只保存到数据库，不再考虑CSV写入
            if self.use_db:
                self._save_trade_to_db(trade_data)
            
            # 保存到Parquet（可选）
            if self.use_parquet:
                self._save_to_parquet('trades', trade_data)
            
            # 清除缓存
            if 'trades' in self.cache:
                del self.cache['trades']
            
            return True
        except Exception as e:
            logger.error(f"保存交易记录失败: {str(e)}")
            return False
    
    def save_analysis(self, analysis_data):
        """保存分析记录
        
        Args:
            analysis_data: 包含分析信息的字典
        """
        try:
            # 只保存到数据库，不再考虑CSV写入
            if self.use_db:
                self._save_analysis_to_db(analysis_data)
            
            # 保存到Parquet（可选）
            if self.use_parquet:
                self._save_to_parquet('analysis', analysis_data)
            
            # 清除缓存
            if 'analysis' in self.cache:
                del self.cache['analysis']
            
            return True
        except Exception as e:
            logger.error(f"保存分析记录失败: {str(e)}")
            return False
    
    def _validate_trade_data(self, data):
        """验证交易数据的完整性和一致性"""
        required_fields = ['Symbol', 'Timestamp', 'Action', 'Price', 'Volume']
        for field in required_fields:
            if field not in data or data[field] is None:
                logger.warning(f"交易数据缺少必要字段: {field}")
                return False
        
        # 验证数据类型
        try:
            float(data['Price'])
            float(data['Volume'])
        except (ValueError, TypeError):
            logger.warning("价格或数量不是有效的数值")
            return False
        
        return True
    
    def save_trades_batch(self, trade_data_list):
        """批量保存交易记录到数据库"""
        if not trade_data_list:
            return True

        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            # 获取数据库表结构中的字段
            cursor.execute("PRAGMA table_info(trades)")
            db_fields = [row[1].lower() for row in cursor.fetchall()]

            # 准备批量插入的数据
            batch_values = []
            valid_fields = []

            # 确定要插入的字段（基于第一条记录和数据库结构）
            for field in TRADE_FIELDNAMES:
                field_lower = field.lower()
                if field_lower in db_fields:
                    valid_fields.append(field_lower)

            if not valid_fields:
                logger.warning("没有有效的交易数据字段可以保存到数据库")
                conn.close()
                return False

            # 为每条记录准备数据
            for trade_data in trade_data_list:
                row_values = []
                for field in TRADE_FIELDNAMES:
                    field_lower = field.lower()
                    if field_lower in valid_fields:
                        if field in trade_data and trade_data[field] is not None:
                            row_values.append(trade_data[field])
                        else:
                            row_values.append(None)  # 使用NULL作为默认值
                batch_values.append(row_values)

            # 执行批量插入
            sql = f"INSERT INTO trades ({', '.join(valid_fields)}) VALUES ({', '.join(['?'] * len(valid_fields))})"
            cursor.executemany(sql, batch_values)
            conn.commit()

            logger.info(f"批量保存{len(trade_data_list)}条交易记录成功")
            conn.close()
            return True

        except Exception as e:
            logger.error(f"批量保存交易记录失败: {str(e)}")
            if 'conn' in locals():
                conn.close()
            return False

    def _save_trade_to_db(self, trade_data):
        """保存交易记录到数据库"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # 准备SQL语句和参数
            fields = []
            placeholders = []
            values = []
            
            # 获取数据库表结构中的字段
            cursor.execute("PRAGMA table_info(trades)")
            db_fields = [row[1].lower() for row in cursor.fetchall()]
            
            # 记录字段映射情况
            logger.info(f"保存交易记录: {trade_data.get('Symbol', 'Unknown')}, {trade_data.get('Action', 'Unknown')}, 价格: {trade_data.get('Price', 0)}")
            
            # 为所有字段设置默认值
            for field in TRADE_FIELDNAMES:
                field_lower = field.lower()
                
                # 检查字段是否存在于数据库表结构中
                if field_lower in db_fields:
                    if field in trade_data and trade_data[field] is not None:
                        fields.append(field_lower)
                        placeholders.append('?')
                        values.append(trade_data[field])
                        logger.debug(f"字段 {field} 有值: {trade_data[field]}")
                    else:
                        # 对于缺失的字段，使用默认值
                        fields.append(field_lower)
                        placeholders.append('?')
                        values.append(None)  # 使用NULL作为默认值
                        logger.debug(f"字段 {field} 缺失，使用默认值NULL")
            
            if not fields:
                logger.warning("没有有效的交易数据字段可以保存到数据库")
                conn.close()
                return False
                
            sql = f"INSERT INTO trades ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
            logger.debug(f"执行SQL: {sql}")
            logger.debug(f"参数值: {values}")
            
            cursor.execute(sql, values)
            conn.commit()
            conn.close()
            logger.info(f"交易记录保存成功: {trade_data.get('Symbol', 'Unknown')}, ID: {cursor.lastrowid}")
            return True
        except Exception as e:
            logger.error(f"保存交易记录到数据库失败: {str(e)}", exc_info=True)
            return False
    
    def _save_analysis_to_db(self, analysis_data):
        """保存分析记录到数据库"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # 准备SQL语句和参数
            fields = []
            placeholders = []
            values = []
            
            # 获取数据库表结构中的字段
            cursor.execute("PRAGMA table_info(analysis)")
            db_fields = [row[1].lower() for row in cursor.fetchall()]
            
            # 记录字段映射情况
            logger.info(f"保存分析记录: {analysis_data.get('Symbol', 'Unknown')}, 时间: {analysis_data.get('Timestamp', 'Unknown')}")
            
            # 为所有字段设置默认值
            for field in ANALYSIS_FIELDNAMES:
                field_lower = field.lower()
                
                # 检查字段是否存在于数据库表结构中
                if field_lower in db_fields:
                    if field in analysis_data and analysis_data[field] is not None:
                        fields.append(field_lower)
                        placeholders.append('?')
                        values.append(analysis_data[field])
                        logger.debug(f"字段 {field} 有值: {analysis_data[field]}")
                    else:
                        # 对于缺失的字段，使用默认值
                        fields.append(field_lower)
                        placeholders.append('?')
                        values.append(None)  # 使用NULL作为默认值
                        logger.debug(f"字段 {field} 缺失，使用默认值NULL")
            
            # 处理可能存在的额外字段
            for field, value in analysis_data.items():
                field_lower = field.lower()
                if field_lower in db_fields and field_lower not in [f.lower() for f in fields]:
                    fields.append(field_lower)
                    placeholders.append('?')
                    values.append(value)
                    logger.debug(f"额外字段 {field} 有值: {value}")
            
            if not fields:
                logger.warning("没有有效的分析数据字段可以保存到数据库")
                conn.close()
                return False
                
            sql = f"INSERT INTO analysis ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
            logger.debug(f"执行SQL: {sql}")
            logger.debug(f"参数值: {values}")
            
            cursor.execute(sql, values)
            conn.commit()
            conn.close()
            logger.info(f"分析记录保存成功: {analysis_data.get('Symbol', 'Unknown')}, ID: {cursor.lastrowid}")
            return True
        except Exception as e:
            logger.error(f"保存分析记录到数据库失败: {str(e)}", exc_info=True)
            return False
    
    def _save_to_parquet(self, data_type, data_row_dict):
        """保存数据到Parquet文件"""
        if not self.use_parquet:
            return False
        
        try:
            # 确定文件路径
            parquet_dir = os.path.join(DATA_DIR, 'parquet')
            os.makedirs(parquet_dir, exist_ok=True)
            
            # 按月分区
            month = data_row_dict['Timestamp'][:7]  # 提取YYYY-MM
            parquet_file = os.path.join(parquet_dir, f"{data_type}_{month}.parquet")
            
            # 转换为DataFrame
            df_row = pd.DataFrame([data_row_dict])
            
            # 如果文件已存在，追加数据
            if os.path.exists(parquet_file):
                existing_df = pd.read_parquet(parquet_file)
                df = pd.concat([existing_df, df_row], ignore_index=True)
            else:
                df = df_row
            
            # 保存到Parquet文件
            df.to_parquet(parquet_file, index=False)
            return True
        except Exception as e:
            logger.error(f"保存到Parquet文件失败: {str(e)}")
            return False
    
    def get_trades(self, symbol=None, start_date=None, end_date=None):
        """获取交易记录
        
        Args:
            symbol: 股票代码（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            
        Returns:
            DataFrame: 交易记录
        """
        # 尝试从缓存获取
        cache_key = f"trades_{symbol}_{start_date}_{end_date}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            if self.use_db:
                df = self._get_trades_from_db(symbol, start_date, end_date)
            else:
                df = self._get_trades_from_csv(symbol, start_date, end_date)
            
            # 更新缓存
            if len(self.cache) >= self.cache_size:
                # 移除最早的缓存项
                self.cache.pop(next(iter(self.cache)))
            self.cache[cache_key] = df
            
            return df
        except Exception as e:
            logger.error(f"获取交易记录失败: {str(e)}")
            return pd.DataFrame()
    
    def _get_trades_from_db(self, symbol=None, start_date=None, end_date=None):
        """从数据库获取交易记录"""
        try:
            conn = sqlite3.connect(DB_FILE)
            
            # 构建SQL查询
            sql = "SELECT * FROM trades"
            conditions = []
            params = []
            
            if symbol:
                conditions.append("symbol = ?")
                params.append(symbol)
            
            if start_date:
                conditions.append("timestamp >= ?")
                params.append(start_date)
            
            if end_date:
                conditions.append("timestamp <= ?")
                params.append(end_date)
            
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
            
            # 执行查询
            df = pd.read_sql_query(sql, conn, params=params)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"从数据库获取交易记录失败: {str(e)}")
            return pd.DataFrame()
    
    def _get_trades_from_csv(self, symbol=None, start_date=None, end_date=None):
        """从CSV文件获取交易记录"""
        try:
            if not os.path.exists(TRADE_LOG_FILE):
                logger.warning(f"交易日志文件不存在: {TRADE_LOG_FILE}")
                return pd.DataFrame()
            
            df = pd.read_csv(TRADE_LOG_FILE)
            
            # 应用过滤条件
            if symbol:
                df = df[df['Symbol'] == symbol]
            
            if start_date:
                df = df[df['Timestamp'] >= start_date]
            
            if end_date:
                df = df[df['Timestamp'] <= end_date]
            
            return df
        except Exception as e:
            logger.error(f"从CSV文件获取交易记录失败: {str(e)}")
            return pd.DataFrame()
    
    def get_analysis(self, symbol=None, start_date=None, end_date=None):
        """获取分析记录
        
        Args:
            symbol: 股票代码（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            
        Returns:
            DataFrame: 分析记录
        """
        # 实现类似get_trades的逻辑
        cache_key = f"analysis_{symbol}_{start_date}_{end_date}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            if self.use_db:
                df = self._get_analysis_from_db(symbol, start_date, end_date)
            else:
                df = self._get_analysis_from_csv(symbol, start_date, end_date)
            
            # 更新缓存
            if len(self.cache) >= self.cache_size:
                self.cache.pop(next(iter(self.cache)))
            self.cache[cache_key] = df
            
            return df
        except Exception as e:
            logger.error(f"获取分析记录失败: {str(e)}")
            return pd.DataFrame()
    
    def _get_analysis_from_db(self, symbol=None, start_date=None, end_date=None):
        """从数据库获取分析记录"""
        # 实现类似_get_trades_from_db的逻辑
        try:
            conn = sqlite3.connect(DB_FILE)
            
            sql = "SELECT * FROM analysis"
            conditions = []
            params = []
            
            if symbol:
                conditions.append("symbol = ?")
                params.append(symbol)
            
            if start_date:
                conditions.append("timestamp >= ?")
                params.append(start_date)
            
            if end_date:
                conditions.append("timestamp <= ?")
                params.append(end_date)
            
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
            
            df = pd.read_sql_query(sql, conn, params=params)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"从数据库获取分析记录失败: {str(e)}")
            return pd.DataFrame()
    
    def _get_analysis_from_csv(self, symbol=None, start_date=None, end_date=None):
        """从CSV文件获取分析记录"""
        # 实现类似_get_trades_from_csv的逻辑
        try:
            if not os.path.exists(ANALYSIS_LOG_FILE):
                logger.warning(f"分析日志文件不存在: {ANALYSIS_LOG_FILE}")
                return pd.DataFrame()
            
            df = pd.read_csv(ANALYSIS_LOG_FILE)
            
            if symbol:
                df = df[df['Symbol'] == symbol]
            
            if start_date:
                df = df[df['Timestamp'] >= start_date]
            
            if end_date:
                df = df[df['Timestamp'] <= end_date]
            
            return df
        except Exception as e:
            logger.error(f"从CSV文件获取分析记录失败: {str(e)}")
            return pd.DataFrame()
    
    def backup_data_files(self):
        """备份数据文件"""
        try:
            backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = os.path.join(BACKUP_DIR, backup_time)
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份CSV文件
            for file in [TRADE_LOG_FILE, ANALYSIS_LOG_FILE]:
                if os.path.exists(file):
                    shutil.copy2(file, os.path.join(backup_dir, os.path.basename(file)))
            
            # 备份数据库
            if os.path.exists(DB_FILE):
                shutil.copy2(DB_FILE, os.path.join(backup_dir, os.path.basename(DB_FILE)))
            
            logger.info(f"数据文件备份成功: {backup_dir}")
            return True
        except Exception as e:
            logger.error(f"备份数据文件失败: {str(e)}")
            return False
    
    def migrate_csv_to_db(self):
        """将CSV数据迁移到数据库"""
        try:
            # 迁移交易数据
            if os.path.exists(TRADE_LOG_FILE):
                df = pd.read_csv(TRADE_LOG_FILE)
                conn = sqlite3.connect(DB_FILE)
                
                # 删除现有数据
                conn.execute("DELETE FROM trades")
                
                # 写入新数据
                df.to_sql('trades', conn, if_exists='append', index=False)
                
                # 迁移分析数据
                if os.path.exists(ANALYSIS_LOG_FILE):
                    df = pd.read_csv(ANALYSIS_LOG_FILE)
                    conn.execute("DELETE FROM analysis")
                    df.to_sql('analysis', conn, if_exists='append', index=False)
                
                conn.commit()
                conn.close()
                
                logger.info("CSV数据成功迁移到数据库")
                return True
            else:
                logger.warning(f"交易日志文件不存在: {TRADE_LOG_FILE}")
                return False
        except Exception as e:
            logger.error(f"迁移CSV数据到数据库失败: {str(e)}")
            return False
    
    def analyze_trade_performance(self):
        """分析交易表现并生成结果"""
        try:
            # 获取所有交易数据
            trades_df = self.get_trades()
            
            if trades_df.empty:
                logger.warning("没有交易数据可供分析")
                return None
            
            # 分离买入和卖出记录
            buy_records = trades_df[trades_df['Action'] == 'BUY']
            sell_records = trades_df[trades_df['Action'] == 'SELL']
            
            # 创建完整交易记录
            complete_trades = []
            
            # 遍历卖出记录，查找对应的买入记录
            for _, sell in sell_records.iterrows():
                symbol = sell['Symbol']
                sell_time = pd.to_datetime(sell['Timestamp'])
                
                # 查找该股票在卖出前的最近一次买入记录
                matching_buys = buy_records[buy_records['Symbol'] == symbol]
                if len(matching_buys) == 0:
                    continue
                    
                matching_buys['Timestamp'] = pd.to_datetime(matching_buys['Timestamp'])
                matching_buys = matching_buys[matching_buys['Timestamp'] < sell_time]
                
                if len(matching_buys) == 0:
                    continue
                    
                # 获取最近的买入记录
                buy = matching_buys.sort_values('Timestamp', ascending=False).iloc[0]
                
                # 合并买入和卖出数据
                trade_record = {
                    # 交易基本信息
                    'Symbol': symbol,
                    'Buy_Time': buy['Timestamp'],
                    'Sell_Time': sell_time,
                    'Holding_Hours': sell['Holding_Hours'],
                    'Buy_Price': buy['Price'],
                    'Sell_Price': sell['Price'],
                    'Volume': buy['Volume'],
                    'Profit_Pct': sell['Net_Profit_Pct_Sell'],
                    'Max_Profit_Pct': sell['Max_Profit_Pct'],
                    'Final_Drawdown_Pct': sell['Final_Drawdown_Pct'],
                    'Sell_Reason': sell['Sell_Reason'],
                    
                    # 买入点指标
                    'TRIX_Buy': buy.get('TRIX_Buy'),
                    'Volatility_Buy': buy.get('Volatility'),
                    'ATR_Pct_Buy': buy.get('ATR_Pct'),
                    'Volatility_Score_Buy': buy.get('Volatility_Score'),
                    'Allocation_Factor_Buy': buy.get('Allocation_Factor'),
                    
                    # 计算其他指标
                    'Trade_Result': 'Win' if sell['Net_Profit_Pct_Sell'] > 0 else 'Loss',
                    'Profit_Amount': (sell['Price'] - buy['Price']) * buy['Volume'],
                }
                
                complete_trades.append(trade_record)
            
            # 创建完整交易DataFrame
            if complete_trades:
                complete_df = pd.DataFrame(complete_trades)
                
                # 保存完整交易记录
                os.makedirs(os.path.dirname(TRADE_RESULTS_FILE), exist_ok=True)
                complete_df.to_csv(TRADE_RESULTS_FILE, index=False)
                logger.info(f"交易分析结果已保存到 {TRADE_RESULTS_FILE}")
                
                return complete_df
            else:
                logger.warning("没有找到匹配的完整交易记录")
                return None
        except Exception as e:
            logger.error(f"分析交易表现失败: {str(e)}")
            return None
    
    def rebuild_database(self):
        """完全重建数据库结构"""
        try:
            # 备份现有数据库
            if os.path.exists(DB_FILE):
                backup_path = f"{DB_FILE}.backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                shutil.copy2(DB_FILE, backup_path)
                logger.info(f"数据库已备份到: {backup_path}")
                
                # 删除现有数据库
                os.remove(DB_FILE)
                logger.info("现有数据库已删除")
            
            # 重新初始化数据库
            self._init_db()
            logger.info("数据库结构已重建")
            return True, "数据库结构已成功重建"
        except Exception as e:
            logger.error(f"重建数据库失败: {str(e)}", exc_info=True)
            return False, f"重建数据库失败: {str(e)}"

# 单例模式
_instance = None

def get_data_manager(use_db=True, use_parquet=False):
    """获取数据管理器实例（单例模式）"""
    global _instance
    if _instance is None:
        _instance = DataManager(use_db, use_parquet)
    return _instance

# 便捷函数
def save_trade(trade_data):
    """全局函数：保存交易记录
    
    Args:
        trade_data: 包含交易信息的字典
    
    Returns:
        bool: 是否成功保存
    """
    data_manager = get_data_manager()
    return data_manager.save_trade(trade_data)

def save_analysis(analysis_data):
    """全局函数：保存分析记录
    
    Args:
        analysis_data: 包含分析信息的字典
    
    Returns:
        bool: 是否成功保存
    """
    data_manager = get_data_manager()
    return data_manager.save_analysis(analysis_data)

def get_trades(symbol=None, start_date=None, end_date=None):
    """获取交易记录"""
    return get_data_manager().get_trades(symbol, start_date, end_date)

def get_analysis(symbol=None, start_date=None, end_date=None):
    """获取分析记录"""
    return get_data_manager().get_analysis(symbol, start_date, end_date)

def backup_data():
    """备份数据"""
    return get_data_manager().backup_data_files()

def analyze_trades():
    """分析交易表现"""
    return get_data_manager().analyze_trade_performance()

def rebuild_database():
    """全局函数：重建数据库"""
    data_manager = get_data_manager()
    return data_manager.rebuild_database()

if __name__ == "__main__":
    # 测试代码
    print("初始化数据管理器...")
    dm = get_data_manager()
    
    # 备份现有数据
    print("备份数据...")
    dm.backup_data_files()
    
    # 如果需要，迁移数据到数据库
    print("迁移CSV数据到数据库...")
    dm.migrate_csv_to_db()
    
    print("数据管理器初始化完成") 