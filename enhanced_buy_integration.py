# coding=utf-8
"""
增强买入逻辑集成模块
将增强指标计算集成到现有的买入逻辑中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_indicators_calculator import EnhancedIndicatorsCalculator
import pandas as pd
from datetime import datetime

class EnhancedBuyIntegration:
    """增强买入逻辑集成器"""
    
    def __init__(self, context):
        self.context = context
        self.calculator = EnhancedIndicatorsCalculator()
        self.context.log.info("增强指标计算器已初始化")
    
    def calculate_and_save_enhanced_indicators(self, symbol, current_price, volume, base_indicators=None):
        """
        计算并保存增强指标到买入记录
        
        参数:
        symbol: 股票代码
        current_price: 当前价格
        volume: 交易量
        base_indicators: 基础指标字典
        
        返回:
        dict: 完整的指标字典
        """
        try:
            # 获取历史数据
            hist_data = self._get_history_data(symbol)
            
            if hist_data is None or len(hist_data) == 0:
                self.context.log.warning(f"无法获取 {symbol} 的历史数据，使用默认指标")
                enhanced_indicators = self.calculator._get_default_indicators()
            else:
                # 计算增强指标
                enhanced_indicators = self.calculator.calculate_all_indicators(
                    symbol, hist_data, current_price, self.context.now
                )
            
            # 合并基础指标和增强指标
            all_indicators = {
                # 基础交易信息
                'timestamp': self.context.now.strftime('%Y-%m-%d %H:%M:%S'),
                'symbol': symbol,
                'action': 'BUY',
                'price': current_price,
                'volume': volume,
                'status': 'ACTIVE'
            }
            
            # 添加基础指标
            if base_indicators:
                all_indicators.update(base_indicators)
            
            # 添加增强指标
            all_indicators.update(enhanced_indicators)
            
            # 保存到数据库
            self._save_to_database(all_indicators)
            
            # 记录日志
            self._log_indicators_summary(symbol, enhanced_indicators)
            
            return all_indicators
            
        except Exception as e:
            self.context.log.error(f"计算增强指标失败 {symbol}: {e}")
            return None
    
    def _get_history_data(self, symbol):
        """获取历史数据"""
        try:
            # 尝试使用优化的历史数据管理器
            if hasattr(self.context, 'history_data_manager'):
                hist_data = self.context.history_data_manager.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=30,  # 获取30天数据，足够计算大部分指标
                    fields=['open', 'high', 'low', 'close', 'volume']
                )
            else:
                # 兼容模式：使用原始data_fetcher
                hist_data = self.context.data_fetcher.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=30,
                    fields=['open', 'high', 'low', 'close', 'volume']
                )
            
            return hist_data
            
        except Exception as e:
            self.context.log.error(f"获取历史数据失败 {symbol}: {e}")
            return None
    
    def _save_to_database(self, indicators):
        """保存指标到数据库"""
        try:
            # 使用正确的数据库保存逻辑
            from scripts.data_manager import save_trade

            # 直接使用全局save_trade函数
            result = save_trade(indicators)

            if result:
                self.context.log.info(f"买入数据已保存到data/trades.db: {indicators.get('Symbol', 'Unknown')}")
            else:
                self.context.log.error(f"买入数据保存失败: {indicators.get('Symbol', 'Unknown')}")
                # 备用保存方法
                self._save_to_database_direct(indicators)

        except Exception as e:
            self.context.log.error(f"保存数据库失败: {e}")
            # 备用保存方法
            try:
                self._save_to_database_direct(indicators)
            except Exception as e2:
                self.context.log.error(f"备用保存也失败: {e2}")
    
    def _save_to_database_direct(self, indicators):
        """直接保存到数据库的备用方法"""
        import sqlite3

        try:
            conn = sqlite3.connect('data/trades.db')
            cursor = conn.cursor()

            # 获取trades表的实际字段
            cursor.execute("PRAGMA table_info(trades)")
            table_columns = [row[1].lower() for row in cursor.fetchall()]

            # 映射字段名（处理大小写和命名差异）
            field_mapping = {
                'Timestamp': 'timestamp',
                'Symbol': 'symbol',
                'Action': 'action',
                'Price': 'price',
                'Volume': 'volume',
                'RSI': 'rsi',
                'MACD_Signal': 'macd_signal',
                'Trend_Strength_Buy': 'trend_strength_buy',
                'MA3_Buy': 'ma3_buy',
                'MA7_Buy': 'ma7_buy',
                'TRIX_Buy': 'trix_buy',
                'Volatility': 'volatility',
                'ATR_Pct': 'atr_pct',
                'Volatility_Score': 'volatility_score',
                'Allocation_Factor': 'allocation_factor',
                'Amplitude_Pct': 'amplitude_pct',
                'Technical_Score': 'technical_score',
                'Overall_Score': 'overall_score'
            }

            # 构建有效的字段和值
            valid_columns = []
            valid_values = []

            for key, value in indicators.items():
                # 获取映射后的字段名
                mapped_key = field_mapping.get(key, key.lower())

                # 检查字段是否存在于表中
                if mapped_key in table_columns:
                    valid_columns.append(mapped_key)
                    valid_values.append(value)

            if valid_columns:
                # 构建插入语句
                placeholders = ', '.join(['?' for _ in valid_columns])
                sql = f"INSERT INTO trades ({', '.join(valid_columns)}) VALUES ({placeholders})"

                cursor.execute(sql, valid_values)
                conn.commit()

                self.context.log.info(f"备用方法保存成功: {indicators.get('Symbol', 'Unknown')}")
            else:
                self.context.log.error("没有有效字段可以保存")

            conn.close()

        except Exception as e:
            self.context.log.error(f"备用保存方法失败: {e}")
            import traceback
            self.context.log.error(f"详细错误: {traceback.format_exc()}")
    
    def _log_indicators_summary(self, symbol, indicators):
        """记录指标摘要日志"""
        try:
            # 选择关键指标进行日志记录
            key_indicators = {
                '涨跌幅': indicators.get('price_change_pct', 0),
                '日内振幅': indicators.get('intraday_range_pct', 0),
                '5日均量比': indicators.get('volume_ma5_ratio', 1),
                'RSI(5)': indicators.get('rsi_5d', 50),
                'MACD柱': indicators.get('macd_histogram', 0),
                '技术得分': indicators.get('technical_score', 50),
                '总体得分': indicators.get('overall_score', 50)
            }
            
            summary = ', '.join([f"{k}={v:.2f}" for k, v in key_indicators.items()])
            self.context.log.info(f"{symbol} 增强指标: {summary}")
            
        except Exception as e:
            self.context.log.debug(f"记录指标摘要失败: {e}")

def integrate_enhanced_indicators_to_main():
    """
    集成增强指标到主策略的示例代码
    这个函数展示如何在main.py中集成增强指标
    """
    
    integration_code = '''
# 在main.py的初始化部分添加
def initialize(context):
    # ... 现有初始化代码 ...
    
    # 初始化增强指标集成器
    from enhanced_buy_integration import EnhancedBuyIntegration
    context.enhanced_buy_integration = EnhancedBuyIntegration(context)
    context.log.info("增强买入指标集成器已初始化")

# 在买入逻辑中集成增强指标计算
def execute_buy_order(context, symbol, price, volume):
    """执行买入订单并保存增强指标"""
    
    try:
        # 1. 执行原有的买入逻辑
        # ... 现有买入代码 ...
        
        # 2. 准备基础指标
        base_indicators = {
            'trend_strength_buy': calculate_trend_strength(symbol),
            'ma3_buy': calculate_ma(symbol, 3),
            'ma7_buy': calculate_ma(symbol, 7),
            'trix_buy': calculate_trix(symbol),
            'volatility': calculate_volatility(symbol),
            'atr_pct': calculate_atr_pct(symbol),
            'volatility_score': calculate_volatility_score(symbol),
            'allocation_factor': calculate_allocation_factor(symbol),
            'ma_cross_buy_signal_buy': check_ma_cross_signal(symbol)
        }
        
        # 3. 计算并保存增强指标
        all_indicators = context.enhanced_buy_integration.calculate_and_save_enhanced_indicators(
            symbol=symbol,
            current_price=price,
            volume=volume,
            base_indicators=base_indicators
        )
        
        if all_indicators:
            context.log.info(f"成功保存 {symbol} 的增强买入指标")
        else:
            context.log.warning(f"保存 {symbol} 增强指标失败，使用基础指标")
            # 备用：保存基础指标
            save_basic_buy_record(context, symbol, price, volume, base_indicators)
        
    except Exception as e:
        context.log.error(f"执行增强买入逻辑失败 {symbol}: {e}")
        # 备用：执行原有买入逻辑
        execute_original_buy_logic(context, symbol, price, volume)

# 在买入信号触发处调用
def handle_buy_signal(context, symbol, signal_info):
    """处理买入信号"""
    
    try:
        # 获取当前价格和计算交易量
        current_price = signal_info['current_price']
        volume = calculate_buy_volume(context, symbol, current_price)
        
        # 执行增强买入逻辑
        execute_buy_order(context, symbol, current_price, volume)
        
        context.log.info(f"买入信号处理完成: {symbol} @ {current_price}")
        
    except Exception as e:
        context.log.error(f"处理买入信号失败 {symbol}: {e}")
'''
    
    return integration_code

def create_test_script():
    """创建测试脚本"""
    
    test_code = '''
# 测试增强指标计算
def test_enhanced_indicators():
    """测试增强指标计算功能"""
    
    from enhanced_indicators_calculator import EnhancedIndicatorsCalculator
    import pandas as pd
    import numpy as np
    from datetime import datetime
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    np.random.seed(42)
    
    test_data = pd.DataFrame({
        'open': 10 + np.random.randn(30) * 0.5,
        'high': 10.5 + np.random.randn(30) * 0.5,
        'low': 9.5 + np.random.randn(30) * 0.5,
        'close': 10 + np.random.randn(30) * 0.5,
        'volume': 1000000 + np.random.randint(-200000, 200000, 30)
    })
    
    # 确保价格逻辑正确
    test_data['high'] = np.maximum(test_data['high'], test_data[['open', 'close']].max(axis=1))
    test_data['low'] = np.minimum(test_data['low'], test_data[['open', 'close']].min(axis=1))
    
    # 创建计算器
    calculator = EnhancedIndicatorsCalculator()
    
    # 计算指标
    indicators = calculator.calculate_all_indicators(
        symbol='TEST001',
        data=test_data,
        current_price=10.5,
        current_time=datetime.now()
    )
    
    # 显示结果
    print("=== 增强指标计算测试结果 ===")
    for category in ['price', 'volume', 'technical', 'risk', 'time']:
        print(f"\\n{category.upper()} 指标:")
        for key, value in indicators.items():
            if category in key or (category == 'price' and 'price' in key):
                print(f"  {key}: {value}")
    
    print(f"\\n总计算指标数量: {len(indicators)}")
    return indicators

if __name__ == "__main__":
    test_enhanced_indicators()
'''
    
    return test_code

def main():
    """主函数"""
    print("🚀 增强买入逻辑集成模块")
    print("="*50)
    
    print("📋 功能说明:")
    print("1. EnhancedBuyIntegration类 - 集成增强指标到买入逻辑")
    print("2. 自动计算108个增强指标")
    print("3. 保存到扩展后的数据库")
    print("4. 提供集成示例代码")
    
    print("\n🔧 集成步骤:")
    print("1. 在main.py初始化中添加EnhancedBuyIntegration")
    print("2. 在买入逻辑中调用calculate_and_save_enhanced_indicators")
    print("3. 传入基础指标和交易信息")
    print("4. 自动计算并保存所有增强指标")
    
    print("\n📊 指标类别:")
    print("• 价格指标: 涨跌幅、动量、振幅等")
    print("• 成交量指标: 均量比、量价关系等")
    print("• 技术指标: RSI、MACD、布林带等")
    print("• 形态指标: 支撑阻力、K线形态等")
    print("• 时间指标: 交易时段、季节性等")
    print("• 风险指标: 回撤、夏普比率等")
    print("• 综合得分: 技术面、动量、趋势等")
    
    print("\n✅ 模块准备完成，可以集成到主策略中!")

if __name__ == "__main__":
    main()
